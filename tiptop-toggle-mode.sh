#!/bin/bash
# Unified script to toggle between test and production modes for both server and extension
# Also allows toggling between mock data and real API for the AI part

# Function to handle errors
handle_error() {
    echo "Error occurred in toggle script at line $1"
    exit 1
}

trap 'handle_error $LINENO' ERR

# Function to show usage
show_usage() {
    echo "Usage: $0 [test|production] [--mock-data|--real-api]"
    echo ""
    echo "Mode options:"
    echo "  test       - Switch to test mode"
    echo "  production - Switch to production mode"
    echo ""
    echo "AI data options (optional):"
    echo "  --mock-data  - Use mock data for AI responses (default in test mode)"
    echo "  --real-api   - Use real API for AI responses (default in production mode)"
    echo ""
    echo "Examples:"
    echo "  $0 test                 - Switch to test mode with mock data"
    echo "  $0 test --real-api      - Switch to test mode with real API"
    echo "  $0 production           - Switch to production mode with real API"
    echo "  $0 production --mock-data - Switch to production mode with mock data"
    exit 1
}

# Parse command line arguments
if [ "$1" == "test" ]; then
    echo "Switching to TEST mode..."
    MODE="test"
    MODE_VALUE="true"
    # Default to mock data in test mode
    USE_MOCK_DATA="true"
elif [ "$1" == "production" ]; then
    echo "Switching to PRODUCTION mode..."
    MODE="production"
    MODE_VALUE="false"
    # Default to real API in production mode
    USE_MOCK_DATA="false"
else
    show_usage
fi

# Check for additional arguments
if [ "$2" == "--mock-data" ]; then
    echo "Using MOCK DATA for AI responses..."
    USE_MOCK_DATA="true"
elif [ "$2" == "--real-api" ]; then
    echo "Using REAL API for AI responses..."
    USE_MOCK_DATA="false"
elif [ -n "$2" ]; then
    echo "Unknown option: $2"
    show_usage
fi

# Create a mode indicator file that the extension can check
echo "Creating mode indicator file..."
echo "{\"mode\":\"$MODE\",\"isTestMode\":$MODE_VALUE,\"useMockData\":$USE_MOCK_DATA,\"timestamp\":\"$(date -u +"%Y-%m-%dT%H:%M:%SZ")\"}" > tiptop-mode.json

# Update server configuration
echo "Updating server configuration..."
cd tiptop-server

# Check if .env file exists, create it if not
if [ ! -f .env ]; then
    echo "Creating .env file..."
    touch .env
fi

# Function to set environment variable in .env file
set_env_var() {
    local key=$1
    local value=$2
    
    # Check if the variable already exists in the file
    if grep -q "^${key}=" .env; then
        # Replace the existing value
        sed -i "" "s|^${key}=.*|${key}=${value}|" .env
    else
        # Add the variable
        echo "${key}=${value}" >> .env
    fi
}

# Set the environment variables
set_env_var "TIPTOP_TEST_MODE" "$MODE_VALUE"
set_env_var "TIPTOP_USE_MOCK_DATA" "$USE_MOCK_DATA"

# Export the environment variables for this session
export TIPTOP_TEST_MODE=$MODE_VALUE
export TIPTOP_USE_MOCK_DATA=$USE_MOCK_DATA

# Update Kubernetes secrets
echo "Updating Kubernetes secrets..."
if [ "$MODE" == "test" ]; then
    echo "Using test secrets..."
    kubectl apply -f k8s-tiptop/tiptop-postgres-secrets-test.yaml -n tiptop
    kubectl apply -f k8s-tiptop/tiptop-cloud-function-secrets-test.yaml -n tiptop
    kubectl apply -f websocket-server/k8s-deploy/tiptop-websocket-secrets-test.yaml -n tiptop
else
    echo "Using production secrets..."
    kubectl apply -f k8s-tiptop/tiptop-postgres-secrets.yaml -n tiptop
    kubectl apply -f k8s-tiptop/tiptop-cloud-function-secrets.yaml -n tiptop
    kubectl apply -f websocket-server/k8s-deploy/tiptop-websocket-secrets.yaml -n tiptop
fi

# Restart deployments to pick up new configuration
echo "Restarting deployments..."
kubectl rollout restart deployment/cloud-function-tiptop-deployment -n tiptop
kubectl rollout restart deployment/tiptop-websocket-deployment -n tiptop

# Wait for deployments to be ready
echo "Waiting for deployments to be ready..."
kubectl rollout status deployment/cloud-function-tiptop-deployment -n tiptop
kubectl rollout status deployment/tiptop-websocket-deployment -n tiptop

# Return to root directory
cd ..

# Update extension configuration
echo "Updating extension configuration..."
cd tiptop-extension

# Set the mock data flag in chrome.storage.local
# This is a placeholder - we can't directly modify chrome.storage from a shell script
# The extension will read the mode indicator file instead
echo "Extension will read configuration from tiptop-mode.json"

# Return to root directory
cd ..

echo "Mode switched to $MODE successfully!"
echo "Current configuration:"
echo "  - Test Mode: $([[ "$MODE" == "test" ]] && echo "ENABLED" || echo "DISABLED")"
echo "  - Mock Data: $([[ "$USE_MOCK_DATA" == "true" ]] && echo "ENABLED" || echo "DISABLED")"
echo "  - Cloud Function URL: $([[ "$MODE" == "test" ]] && echo "http://localhost:30080" || echo "https://tiptop.qubitrhythm.com")"
echo "  - WebSocket URL: $([[ "$MODE" == "test" ]] && echo "ws://localhost:8080" || echo "wss://ws.tiptop.qubitrhythm.com")"
echo ""
echo "The extension will automatically detect this mode change when reloaded."
echo "To manually set the extension mode, use the toggle in the extension popup."
