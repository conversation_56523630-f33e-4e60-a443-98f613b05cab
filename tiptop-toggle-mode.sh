#!/bin/bash
# Unified script to toggle between test and production modes for both server and extension
# Also allows toggling between mock data and real API for the AI part

# Function to handle errors
handle_error() {
    echo "Error occurred in toggle script at line $1"
    exit 1
}

trap 'handle_error $LINENO' ERR

# Function to cleanup local services
cleanup_local_services() {
    echo "Cleaning up local services..."

    # Stop WebSocket server if running locally
    if lsof -i :8080 >/dev/null 2>&1; then
        echo "Stopping local WebSocket server..."
        lsof -ti :8080 | xargs kill -9 2>/dev/null || true
    fi

    # Stop PostgreSQL port forwarding if running
    if pgrep -f "kubectl port-forward.*tiptop-postgres" >/dev/null 2>&1; then
        echo "Stopping PostgreSQL port forwarding..."
        pkill -f "kubectl port-forward.*tiptop-postgres" 2>/dev/null || true
    fi
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [test|production] [--mock-data|--real-api]"
    echo ""
    echo "Mode options:"
    echo "  test       - Switch to test mode"
    echo "  production - Switch to production mode"
    echo ""
    echo "AI data options (optional):"
    echo "  --mock-data  - Use mock data for AI responses (default in test mode)"
    echo "  --real-api   - Use real API for AI responses (default in production mode)"
    echo ""
    echo "Examples:"
    echo "  $0 test                 - Switch to test mode with mock data"
    echo "  $0 test --real-api      - Switch to test mode with real API"
    echo "  $0 production           - Switch to production mode with real API"
    echo "  $0 production --mock-data - Switch to production mode with mock data"
    exit 1
}

# Parse command line arguments
if [ "$1" == "test" ]; then
    echo "Switching to TEST mode..."
    MODE="test"
    MODE_VALUE="true"
    # Default to mock data in test mode
    USE_MOCK_DATA="true"
elif [ "$1" == "production" ]; then
    echo "Switching to PRODUCTION mode..."
    MODE="production"
    MODE_VALUE="false"
    # Default to real API in production mode
    USE_MOCK_DATA="false"
else
    show_usage
fi

# Check for additional arguments
if [ "$2" == "--mock-data" ]; then
    echo "Using MOCK DATA for AI responses..."
    USE_MOCK_DATA="true"
elif [ "$2" == "--real-api" ]; then
    echo "Using REAL API for AI responses..."
    USE_MOCK_DATA="false"
elif [ -n "$2" ]; then
    echo "Unknown option: $2"
    show_usage
fi

# Cleanup any existing local services
cleanup_local_services

# Create a mode indicator file that the extension can check
echo "Creating mode indicator file..."
echo "{\"mode\":\"$MODE\",\"isTestMode\":$MODE_VALUE,\"useMockData\":$USE_MOCK_DATA,\"timestamp\":\"$(date -u +"%Y-%m-%dT%H:%M:%SZ")\"}" > tiptop-mode.json

# Update server configuration
echo "Updating server configuration..."
cd tiptop-server

# Check if .env file exists, create it if not
if [ ! -f .env ]; then
    echo "Creating .env file..."
    touch .env
fi

# Function to set environment variable in .env file
set_env_var() {
    local key=$1
    local value=$2
    
    # Check if the variable already exists in the file
    if grep -q "^${key}=" .env; then
        # Replace the existing value
        sed -i "" "s|^${key}=.*|${key}=${value}|" .env
    else
        # Add the variable
        echo "${key}=${value}" >> .env
    fi
}

# Set the environment variables
set_env_var "TIPTOP_TEST_MODE" "$MODE_VALUE"
set_env_var "TIPTOP_USE_MOCK_DATA" "$USE_MOCK_DATA"

# Export the environment variables for this session
export TIPTOP_TEST_MODE=$MODE_VALUE
export TIPTOP_USE_MOCK_DATA=$USE_MOCK_DATA

# Update Kubernetes secrets
echo "Updating Kubernetes secrets..."
if [ "$MODE" == "test" ]; then
    echo "Using test secrets..."
    kubectl apply -f k8s-tiptop/tiptop-postgres-secrets-test.yaml -n tiptop
    kubectl apply -f k8s-tiptop/tiptop-cloud-function-secrets-test.yaml -n tiptop
    kubectl apply -f websocket-server/k8s-deploy/tiptop-websocket-secrets-test.yaml -n tiptop
else
    echo "Using production secrets..."
    kubectl apply -f k8s-tiptop/tiptop-postgres-secrets.yaml -n tiptop
    kubectl apply -f k8s-tiptop/tiptop-cloud-function-secrets.yaml -n tiptop
    kubectl apply -f websocket-server/k8s-deploy/tiptop-websocket-secrets.yaml -n tiptop
fi

# Restart deployments to pick up new configuration
echo "Restarting deployments..."
kubectl rollout restart deployment/cloud-function-tiptop-deployment -n tiptop

# Check if WebSocket deployment exists, if not, start WebSocket server locally
if kubectl get deployment tiptop-websocket -n tiptop >/dev/null 2>&1; then
    echo "Restarting WebSocket deployment..."
    kubectl rollout restart deployment/tiptop-websocket -n tiptop
else
    echo "WebSocket deployment not found - starting local WebSocket server..."

    # Check if WebSocket server is already running
    if lsof -i :8080 >/dev/null 2>&1; then
        echo "Port 8080 is already in use - stopping existing process..."
        # Kill any process using port 8080
        lsof -ti :8080 | xargs kill -9 2>/dev/null || true
        sleep 2
    fi

    # Check if PostgreSQL port forwarding is needed
    if ! nc -z localhost 5432 >/dev/null 2>&1; then
        echo "Setting up PostgreSQL port forwarding..."
        kubectl port-forward service/tiptop-postgres 5432:5432 -n tiptop >/dev/null 2>&1 &
        PG_PORT_FORWARD_PID=$!
        echo "PostgreSQL port forwarding started with PID: $PG_PORT_FORWARD_PID"
        sleep 2
    else
        echo "PostgreSQL is already accessible on localhost:5432"
    fi

    # Install parent directory dependencies first (for config.js)
    echo "Installing server dependencies..."
    npm install >/dev/null 2>&1

    # Start WebSocket server in background
    cd websocket-server
    echo "Installing WebSocket server dependencies..."
    npm install >/dev/null 2>&1

    echo "Starting WebSocket server on port 8080..."
    # Create .env file for WebSocket server
    cat > .env << EOF
# Server configuration
PORT=8080

# Database configuration (using Kubernetes PostgreSQL)
DB_USER=postgres
DB_HOST=localhost
DB_NAME=tiptop
DB_PASSWORD=Happy4WS$
DB_PORT=5432

# Set to true to enable detailed logging
DEBUG=true
EOF

    # Start the server in background
    nohup npm start > websocket-server.log 2>&1 &
    WEBSOCKET_PID=$!
    echo "WebSocket server started with PID: $WEBSOCKET_PID"
    echo "Logs available at: websocket-server/websocket-server.log"

    # Wait a moment for server to start
    sleep 3

    # Check if server started successfully
    if lsof -i :8080 >/dev/null 2>&1; then
        echo "✅ WebSocket server is running on port 8080"
    else
        echo "❌ Failed to start WebSocket server"
        echo "Check logs: tail -f websocket-server/websocket-server.log"
    fi

    cd ..
fi

# Wait for deployments to be ready
echo "Waiting for deployments to be ready..."
kubectl rollout status deployment/cloud-function-tiptop-deployment -n tiptop

# Wait for WebSocket deployment if it exists
if kubectl get deployment tiptop-websocket -n tiptop >/dev/null 2>&1; then
    kubectl rollout status deployment/tiptop-websocket -n tiptop
fi

# Return to root directory
cd ..

# Update extension configuration
echo "Updating extension configuration..."
cd tiptop-extension

# Copy the mode indicator file to the extension directory
echo "Copying mode indicator file to extension directory..."
cp ../tiptop-mode.json ./tiptop-mode.json

# Set the mock data flag in chrome.storage.local
# This is a placeholder - we can't directly modify chrome.storage from a shell script
# The extension will read the mode indicator file instead
echo "Extension will read configuration from tiptop-mode.json"

# Return to root directory
cd ..

echo "Mode switched to $MODE successfully!"
echo "Current configuration:"
echo "  - Test Mode: $([[ "$MODE" == "test" ]] && echo "ENABLED" || echo "DISABLED")"
echo "  - Mock Data: $([[ "$USE_MOCK_DATA" == "true" ]] && echo "ENABLED" || echo "DISABLED")"
echo "  - Cloud Function URL: $([[ "$MODE" == "test" ]] && echo "http://localhost:30080" || echo "https://tiptop.qubitrhythm.com")"
echo "  - WebSocket URL: $([[ "$MODE" == "test" ]] && echo "ws://localhost:8080" || echo "wss://ws.tiptop.qubitrhythm.com")"
echo ""

# Check service status
echo "Service Status:"
if curl -s http://localhost:30080/ >/dev/null 2>&1; then
    echo "  ✅ Cloud Function API: Running"
else
    echo "  ❌ Cloud Function API: Not accessible"
fi

if lsof -i :8080 >/dev/null 2>&1; then
    echo "  ✅ WebSocket Server: Running"
else
    echo "  ❌ WebSocket Server: Not running"
fi

if nc -z localhost 5432 >/dev/null 2>&1; then
    echo "  ✅ PostgreSQL: Accessible"
else
    echo "  ❌ PostgreSQL: Not accessible"
fi

echo ""
echo "Next Steps:"
echo "1. Reload the TipTop extension in Chrome (chrome://extensions/)"
echo "2. Test the extension on any webpage"
echo ""
echo "Troubleshooting:"
echo "  - Extension logs: Check browser console on any webpage"
echo "  - API logs: kubectl logs deployment/cloud-function-tiptop-deployment -n tiptop -f"
echo "  - WebSocket logs: tail -f tiptop-server/websocket-server/websocket-server.log"
echo ""
echo "To stop local services:"
echo "  - Stop WebSocket: lsof -ti :8080 | xargs kill -9"
echo "  - Stop PostgreSQL port forwarding: pkill -f 'kubectl port-forward.*tiptop-postgres'"
