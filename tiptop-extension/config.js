/**
 * TipTop Extension Configuration
 *
 * This module provides configuration settings for the TipTop extension,
 * supporting both test and production environments.
 */

// Default test mode setting
let isTestMode = false;
let modeSource = 'default';

// Initialize configuration with default values
let config = {
  // Environment
  isTestMode: false,
  environment: 'production',
  modeSource: modeSource,

  // API URLs
  apiUrls: {
    // Cloud Function endpoints
    tiptop: 'https://tiptop.qubitrhythm.com/tiptop',
    ask: 'https://tiptop.qubitrhythm.com/ask',
    test: 'https://tiptop.qubitrhythm.com/tiptop-test',
  },

  // WebSocket URLs
  wsUrls: [
    // Primary WebSocket server
    'wss://ws.tiptop.qubitrhythm.com',

    // Legacy WebSocket endpoints (for backward compatibility)
    'wss://tiptop.qubitrhythm.com/ws',
  ],

  // Feature flags
  features: {
    useMockData: false,  // Set to true to use mock data instead of real API calls
    useMockWebSocket: false,  // Set to true to use mock WebSocket instead of real connection
  },
};

// Function to check for the mode indicator file
async function checkModeIndicatorFile() {
  try {
    // Try to fetch the mode indicator file
    const response = await fetch(chrome.runtime.getURL('../tiptop-mode.json'));

    if (response.ok) {
      const modeData = await response.json();
      console.log('Found mode indicator file:', modeData);

      // Update the test mode based on the file
      isTestMode = modeData.isTestMode === true;
      modeSource = 'indicatorFile';

      // Update mock data setting if present
      if (modeData.useMockData !== undefined) {
        config.features.useMockData = modeData.useMockData === true;
        console.log(`Mock data ${config.features.useMockData ? 'ENABLED' : 'DISABLED'} from indicator file`);
      }

      // Update chrome.storage to match
      chrome.storage.local.set({
        'TIPTOP_TEST_MODE': isTestMode ? 'true' : 'false',
        'TIPTOP_USE_MOCK_DATA': config.features.useMockData ? 'true' : 'false'
      });

      console.log(`Mode set to ${isTestMode ? 'TEST' : 'PRODUCTION'} from indicator file`);

      // Update the configuration
      updateConfig();

      return true;
    }
  } catch (error) {
    console.log('Mode indicator file not found or invalid, using storage setting');
  }

  return false;
}

// Function to update the configuration based on the current mode
function updateConfig() {
  // Preserve the current feature flags
  const currentFeatures = config.features || {};

  config = {
    // Environment
    isTestMode: isTestMode,
    environment: isTestMode ? 'test' : 'production',
    modeSource: modeSource,

    // API URLs
    apiUrls: {
      // Cloud Function endpoints
      tiptop: isTestMode
        ? 'http://localhost:30080/tiptop'
        : 'https://tiptop.qubitrhythm.com/tiptop',
      ask: isTestMode
        ? 'http://localhost:30080/ask'
        : 'https://tiptop.qubitrhythm.com/ask',
      test: isTestMode
        ? 'http://localhost:30080/tiptop-test'
        : 'https://tiptop.qubitrhythm.com/tiptop-test',
    },

    // WebSocket URLs
    wsUrls: [
      // Primary WebSocket server
      isTestMode
        ? 'ws://localhost:8080'
        : 'wss://ws.tiptop.qubitrhythm.com',

      // Legacy WebSocket endpoints (for backward compatibility)
      isTestMode
        ? 'ws://localhost:30081/ws'
        : 'wss://tiptop.qubitrhythm.com/ws',
    ],

    // Feature flags - preserve existing settings
    features: {
      // Use the current mock data setting or default based on mode
      useMockData: currentFeatures.useMockData !== undefined
        ? currentFeatures.useMockData
        : isTestMode, // Default to mock data in test mode, real API in production

      // Use the current mock WebSocket setting or default based on mode
      useMockWebSocket: currentFeatures.useMockWebSocket !== undefined
        ? currentFeatures.useMockWebSocket
        : false, // Default to real WebSocket in both modes
    },
  };

  // Update the global configuration
  window.TipTopConfig = {
    ...config,
    toggleTestMode,
    checkModeIndicatorFile,
  };

  console.log('TipTop configuration updated:', config);
}

// First check for the mode indicator file
checkModeIndicatorFile().then(fileFound => {
  // If no file was found, load from storage
  if (!fileFound) {
    // Load configuration from chrome.storage.local
    chrome.storage.local.get(['TIPTOP_TEST_MODE'], function(result) {
      isTestMode = result.TIPTOP_TEST_MODE === 'true';
      modeSource = 'storage';

      console.log(`Mode set to ${isTestMode ? 'TEST' : 'PRODUCTION'} from storage`);

      // Update the configuration
      updateConfig();
    });
  }
});

// Function to toggle test mode
function toggleTestMode(enabled) {
  isTestMode = enabled;
  modeSource = 'userToggle';

  chrome.storage.local.set({ 'TIPTOP_TEST_MODE': enabled ? 'true' : 'false' });
  console.log(`TipTop test mode ${enabled ? 'enabled' : 'disabled'}`);

  // Update the configuration
  updateConfig();

  // Reload the extension to apply changes
  if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.reload) {
    chrome.runtime.reload();
  } else {
    console.log('Please reload the extension to apply changes');
  }
}

// Export the initial configuration (will be updated when storage is loaded)
window.TipTopConfig = {
  ...config,
  toggleTestMode,
  checkModeIndicatorFile,
};
