#!/bin/bash

# TipTop Old Deployment Cleanup Script
# This script helps clean up old TipTop deployments before deploying the new version

# Function to handle errors
handle_error() {
    echo "❌ Error occurred in cleanup script at line $1"
    exit 1
}

trap 'handle_error $LINENO' ERR

echo "🧹 TipTop Old Deployment Cleanup"
echo "This script will help you clean up old TipTop deployments."
echo ""

# Check if kubectl is available
command -v kubectl >/dev/null 2>&1 || { echo "❌ kubectl is required but not installed."; exit 1; }

echo "🔍 Checking current deployments in tiptop namespace..."
echo ""

# Check if tiptop namespace exists
if kubectl get namespace tiptop >/dev/null 2>&1; then
    echo "📦 Found tiptop namespace. Current resources:"
    echo ""
    
    echo "🚀 Deployments:"
    kubectl get deployments -n tiptop 2>/dev/null || echo "  No deployments found"
    echo ""
    
    echo "🔌 Services:"
    kubectl get services -n tiptop 2>/dev/null || echo "  No services found"
    echo ""
    
    echo "🌐 Ingress:"
    kubectl get ingress -n tiptop 2>/dev/null || echo "  No ingress found"
    echo ""
    
    echo "💾 Persistent Volumes:"
    kubectl get pvc -n tiptop 2>/dev/null || echo "  No PVCs found"
    echo ""
    
    # Ask for confirmation
    echo "⚠️  WARNING: This will delete ALL resources in the tiptop namespace!"
    echo "This includes:"
    echo "  - All deployments and pods"
    echo "  - All services"
    echo "  - All ingress rules"
    echo "  - All config maps and secrets"
    echo "  - All persistent volume claims (DATA WILL BE LOST!)"
    echo ""
    
    read -p "Are you sure you want to proceed? (type 'yes' to confirm): " confirmation
    
    if [ "$confirmation" = "yes" ]; then
        echo ""
        echo "🗑️  Deleting all resources in tiptop namespace..."
        
        # Delete all resources in the namespace
        kubectl delete all --all -n tiptop
        kubectl delete pvc --all -n tiptop
        kubectl delete configmap --all -n tiptop
        kubectl delete secret --all -n tiptop
        kubectl delete ingress --all -n tiptop
        
        echo "✅ Cleanup completed!"
        echo ""
        echo "🎯 Next steps:"
        echo "1. Run the deployment script: ./deploy-to-gcp.sh YOUR_PROJECT_ID"
        echo "2. Or run the toggle script: ./tiptop-toggle-mode.sh production"
        echo ""
    else
        echo "❌ Cleanup cancelled."
        echo ""
        echo "💡 If you want to keep existing data, consider:"
        echo "1. Backing up your database first"
        echo "2. Using the update-only mode: ./deploy-to-gcp.sh YOUR_PROJECT_ID --update-only"
        echo ""
    fi
else
    echo "📦 No tiptop namespace found. Nothing to clean up."
    echo ""
    echo "🎯 You can proceed directly with deployment:"
    echo "./deploy-to-gcp.sh YOUR_PROJECT_ID"
    echo ""
fi

# Check for old deployments in other namespaces
echo "🔍 Checking for TipTop resources in other namespaces..."
echo ""

# Check default namespace
if kubectl get deployments -n default 2>/dev/null | grep -i tiptop >/dev/null; then
    echo "⚠️  Found TipTop-related deployments in 'default' namespace:"
    kubectl get deployments -n default | grep -i tiptop
    echo ""
    echo "💡 You may want to clean these up manually:"
    echo "kubectl delete deployment <deployment-name> -n default"
    echo ""
fi

# Check smartparent namespace (old name)
if kubectl get namespace smartparent >/dev/null 2>&1; then
    echo "⚠️  Found old 'smartparent' namespace. Resources:"
    kubectl get all -n smartparent
    echo ""
    echo "💡 This appears to be the old namespace. You may want to clean it up:"
    echo "kubectl delete namespace smartparent"
    echo ""
fi

echo "🏁 Cleanup script completed!"
