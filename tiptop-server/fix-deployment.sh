#!/bin/bash
set -e

# Delete the existing deployment
echo "Deleting existing deployment..."
kubectl delete deployment cloud-function-tiptop-deployment -n tiptop

# Apply the updated deployment
echo "Applying updated deployment..."
kubectl apply -f k8s-tiptop/cloud-function-tiptop-deployment.yaml -n tiptop

# Wait for the deployment to be ready
echo "Waiting for deployment to be ready..."
kubectl rollout status deployment/cloud-function-tiptop-deployment -n tiptop

echo "Deployment updated successfully!"
echo "Pod status:"
kubectl get pods -n tiptop

# Display service access information
echo "Cloud function TipTop is now available at:"
echo "  - HTTP: http://localhost:30080"
echo "  - HTTPS: https://localhost:30443"
