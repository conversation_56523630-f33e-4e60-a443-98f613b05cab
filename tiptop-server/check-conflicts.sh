#!/bin/bash

# TipTop Conflict Detection Script
# This script checks for potential conflicts with SmartParent deployment

echo "🔍 TipTop vs SmartParent Conflict Detection"
echo "=============================================="
echo ""

# Function to check if a resource exists
check_resource() {
    local resource_type=$1
    local resource_name=$2
    local namespace=$3
    
    if [ -n "$namespace" ]; then
        kubectl get $resource_type $resource_name -n $namespace >/dev/null 2>&1
    else
        kubectl get $resource_type $resource_name >/dev/null 2>&1
    fi
}

# Function to display conflict status
show_conflict_status() {
    local description=$1
    local resource_type=$2
    local resource_name=$3
    local namespace=$4
    local conflict_level=$5
    
    if check_resource $resource_type $resource_name $namespace; then
        echo "⚠️  $conflict_level: $description"
        echo "   Found: $resource_type/$resource_name"
        if [ -n "$namespace" ]; then
            echo "   Namespace: $namespace"
        fi
        echo ""
        return 1
    else
        echo "✅ Safe: $description"
        return 0
    fi
}

echo "📋 Checking Namespace Isolation..."
echo "-----------------------------------"
conflicts=0

# Check namespace isolation
if ! show_conflict_status "TipTop namespace isolation" "namespace" "tiptop" "" "LOW"; then
    ((conflicts++))
    echo "   💡 This is expected and safe - TipTop uses its own namespace"
    echo ""
fi

if show_conflict_status "SmartParent namespace exists" "namespace" "smartparent" "" "INFO"; then
    echo "   💡 SmartParent namespace found - this is normal"
    echo ""
fi

echo "📋 Checking Cluster-Wide Resources..."
echo "------------------------------------"

# Check ClusterIssuer conflicts (these are cluster-wide)
if ! show_conflict_status "TipTop ClusterIssuer" "clusterissuer" "letsencrypt-prod-tiptop" "" "MEDIUM"; then
    ((conflicts++))
fi

if check_resource "clusterissuer" "letsencrypt-prod" ""; then
    echo "⚠️  MEDIUM: SmartParent may have a similar ClusterIssuer"
    echo "   Found: clusterissuer/letsencrypt-prod"
    echo "   💡 Both can coexist, but check for email conflicts"
    echo ""
    ((conflicts++))
fi

# Check for shared ingress classes
echo "📋 Checking Ingress Configuration..."
echo "-----------------------------------"

# Check if nginx ingress controller is shared
if kubectl get ingressclass nginx >/dev/null 2>&1; then
    echo "✅ Shared: nginx IngressClass (this is normal and safe)"
    echo "   Both TipTop and SmartParent can use the same ingress controller"
    echo ""
else
    echo "⚠️  WARNING: nginx IngressClass not found"
    echo "   You may need to install nginx-ingress-controller"
    echo ""
fi

echo "📋 Checking Domain and Certificate Conflicts..."
echo "----------------------------------------------"

# Check for domain conflicts in ingress
echo "🌐 TipTop domains:"
echo "   - tiptop.qubitrhythm.com"
echo "   - ws.tiptop.qubitrhythm.com"
echo ""

# Check SmartParent ingress for domain conflicts
if kubectl get ingress -n smartparent >/dev/null 2>&1; then
    echo "🌐 SmartParent domains found:"
    kubectl get ingress -n smartparent -o jsonpath='{range .items[*]}{.spec.rules[*].host}{"\n"}{end}' 2>/dev/null | sed 's/^/   - /'
    echo ""
    
    # Check for specific domain conflicts
    if kubectl get ingress -n smartparent -o jsonpath='{range .items[*]}{.spec.rules[*].host}{"\n"}{end}' 2>/dev/null | grep -q "tiptop.qubitrhythm.com"; then
        echo "❌ CRITICAL: Domain conflict detected!"
        echo "   Both services trying to use tiptop.qubitrhythm.com"
        echo ""
        ((conflicts++))
    else
        echo "✅ Safe: No domain conflicts detected"
        echo ""
    fi
else
    echo "✅ Safe: No SmartParent ingress found"
    echo ""
fi

echo "📋 Checking Load Balancer and IP Conflicts..."
echo "---------------------------------------------"

# Check for static IP conflicts
if gcloud compute addresses list --global --format="value(name)" 2>/dev/null | grep -q "tiptop"; then
    echo "⚠️  INFO: TipTop static IP addresses found"
    gcloud compute addresses list --global --filter="name~tiptop" --format="table(name,address,status)" 2>/dev/null
    echo ""
fi

if gcloud compute addresses list --global --format="value(name)" 2>/dev/null | grep -q "smartparent"; then
    echo "⚠️  INFO: SmartParent static IP addresses found"
    gcloud compute addresses list --global --filter="name~smartparent" --format="table(name,address,status)" 2>/dev/null
    echo ""
fi

echo "📋 Checking Service Account and RBAC Conflicts..."
echo "-------------------------------------------------"

# Check for service account conflicts
if kubectl get serviceaccount -n tiptop >/dev/null 2>&1; then
    echo "✅ TipTop service accounts (namespace isolated):"
    kubectl get serviceaccount -n tiptop --no-headers | sed 's/^/   /'
    echo ""
fi

if kubectl get serviceaccount -n smartparent >/dev/null 2>&1; then
    echo "✅ SmartParent service accounts (namespace isolated):"
    kubectl get serviceaccount -n smartparent --no-headers | sed 's/^/   /'
    echo ""
fi

echo "📋 Checking Storage Conflicts..."
echo "-------------------------------"

# Check for PVC conflicts (should be namespace isolated)
if kubectl get pvc -n tiptop >/dev/null 2>&1; then
    echo "✅ TipTop storage (namespace isolated):"
    kubectl get pvc -n tiptop --no-headers | sed 's/^/   /'
    echo ""
fi

if kubectl get pvc -n smartparent >/dev/null 2>&1; then
    echo "✅ SmartParent storage (namespace isolated):"
    kubectl get pvc -n smartparent --no-headers | sed 's/^/   /'
    echo ""
fi

echo "📋 Summary"
echo "=========="
echo ""

if [ $conflicts -eq 0 ]; then
    echo "🎉 NO CRITICAL CONFLICTS DETECTED!"
    echo ""
    echo "✅ Safe to deploy TipTop alongside SmartParent"
    echo "✅ Both services use proper namespace isolation"
    echo "✅ No domain or resource conflicts found"
    echo ""
    echo "🚀 You can proceed with deployment:"
    echo "   ./deploy-to-gcp.sh YOUR_PROJECT_ID --rolling-update"
else
    echo "⚠️  $conflicts POTENTIAL CONFLICTS DETECTED"
    echo ""
    echo "📋 Review the conflicts above before deployment"
    echo "💡 Most conflicts can be resolved by:"
    echo "   - Using different domain names"
    echo "   - Updating ClusterIssuer email addresses"
    echo "   - Ensuring proper namespace isolation"
    echo ""
    echo "🔧 After resolving conflicts, you can deploy with:"
    echo "   ./deploy-to-gcp.sh YOUR_PROJECT_ID --rolling-update"
fi

echo ""
echo "📚 For more information, see:"
echo "   - GCP-DEPLOYMENT-GUIDE.md"
echo "   - Kubernetes namespace documentation"
