# TipTop Server

This directory contains the server-side components for the TipTop extension, including the cloud function and WebSocket server.

## Prerequisites

- Docker Desktop with Kubernetes enabled
- kubectl command-line tool
- Chrome browser for testing the extension

## Directory Structure

- `k8s-tiptop/`: TipTop-specific Kubernetes configuration files
- `cloud-function-tiptop/`: TipTop cloud function code
- `websocket-server/`: WebSocket server for social features

## Setup Instructions

1. Start Docker Desktop and ensure Kubernetes is enabled
2. Make the deployment script executable:
   ```
   chmod +x tiptop-local-deploy.sh
   ```
3. Run the deployment script:
   ```
   ./tiptop-local-deploy.sh
   ```
   This script will:
   - Create a directory at `/Users/<USER>/AI/tiptop/tiptop-data` for persistent storage
   - Create a dedicated PV and PVC for the tiptop namespace
   - Deploy PostgreSQL and the cloud function
4. Wait for all services to be up and running
5. Load the extension in Chrome:
   - Open Chrome and navigate to `chrome://extensions/`
   - Enable "Developer mode" (toggle in the top-right corner)
   - Click "Load unpacked" and select the `tiptop-extension` directory
   - The extension should now be loaded with the name "TipTop Dev"

## Testing the Extension

1. Navigate to any webpage
2. Click on the TipTop Dev extension icon in the Chrome toolbar
3. The extension should connect to your local development environment at http://localhost:30080 and display the TipTop panel

The service is configured to use NodePort, which means:
- It's directly accessible at http://localhost:30080 (HTTP) and https://localhost:30443 (HTTPS)
- No port forwarding is needed
- The service remains accessible as long as the Kubernetes cluster is running

## Updating the Cloud Function

If you make changes to the cloud function code, you can update the deployment without redeploying everything:

1. Make your changes to the code in the `cloud-function-tiptop` directory
2. Run the update script:
   ```
   ./tiptop-update.sh
   ```

## Troubleshooting

- If the extension can't connect to the local server, check that the service is running and the NodePort is accessible:
  ```
  kubectl get service cloud-function-tiptop-service -n tiptop
  ```

- If you need to update the service configuration (e.g., change the NodePort):
  ```
  ./update-service.sh
  ```
- To view logs from the cloud function:
  ```
  kubectl logs -f deployment/cloud-function-tiptop-deployment -n tiptop
  ```
- To restart the cloud function:
  ```
  kubectl rollout restart deployment/cloud-function-tiptop-deployment -n tiptop
  ```

## Cleaning Up

To remove all resources created by the deployment script:

```
kubectl delete namespace tiptop
kubectl delete pv tiptop-postgres-pv
```

If you want to completely clean up, you can also remove the data directory:

```
rm -rf /Users/<USER>/AI/tiptop/tiptop-data
```
