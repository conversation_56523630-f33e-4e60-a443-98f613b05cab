apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: tiptop-websocket-ingress
  namespace: tiptop
  annotations:
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "3600"
    nginx.ingress.kubernetes.io/websocket-services: "tiptop-websocket"
spec:
  rules:
  - host: ws.tiptop.DOMAIN_NAME  # Replace with your actual domain
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: tiptop-websocket
            port:
              number: 8080
