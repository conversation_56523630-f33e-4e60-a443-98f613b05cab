apiVersion: apps/v1
kind: Deployment
metadata:
  name: tiptop-websocket
  namespace: tiptop
spec:
  replicas: 1
  selector:
    matchLabels:
      app: tiptop-websocket
  template:
    metadata:
      labels:
        app: tiptop-websocket
    spec:
      containers:
      - name: tiptop-websocket
        image: gcr.io/PROJECT_ID/tiptop-websocket:latest
        ports:
        - containerPort: 8080
        env:
        - name: PORT
          value: "8080"
        - name: DB_USER
          valueFrom:
            secretKeyRef:
              name: postgres-secret
              key: username
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: postgres-secret
              key: password
        - name: DB_HOST
          value: "postgres"
        - name: DB_NAME
          value: "tiptop"
        - name: DB_PORT
          value: "5432"
        resources:
          requests:
            cpu: 100m
            memory: 128Mi
          limits:
            cpu: 300m
            memory: 256Mi
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
