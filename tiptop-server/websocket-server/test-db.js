require('dotenv').config();
const { Pool } = require('pg');

// PostgreSQL connection
const pool = new Pool({
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'tiptop',
  password: process.env.DB_PASSWORD || 'postgres',
  port: parseInt(process.env.DB_PORT || '5432'),
});

async function testConnection() {
  try {
    console.log('Testing database connection...');
    console.log('Connection parameters:', {
      user: process.env.DB_USER || 'postgres',
      host: process.env.DB_HOST || 'localhost',
      database: process.env.DB_NAME || 'tiptop',
      port: process.env.DB_PORT || '5432'
    });

    // Test connection
    const client = await pool.connect();
    console.log('Connected to database successfully!');
    
    // Test query
    const result = await client.query('SELECT NOW() as time');
    console.log('Database time:', result.rows[0].time);
    
    // Release client
    client.release();
    
    // Close pool
    await pool.end();
    
    console.log('Database connection test completed successfully.');
  } catch (err) {
    console.error('Error testing database connection:', err);
  }
}

testConnection();
