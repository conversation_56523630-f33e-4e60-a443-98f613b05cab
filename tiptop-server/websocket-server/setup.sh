#!/bin/bash

# TipTop WebSocket Server Setup Script
# This script can be used for both local development and GCP deployment

# Set default values
ENV=${1:-"local"}  # Default to local if no argument provided
PROJECT_ID=""
DOMAIN_NAME=""

# Function to display usage information
usage() {
  echo "Usage: $0 [local|gcp] [options]"
  echo ""
  echo "Options for GCP deployment:"
  echo "  --project-id=PROJECT_ID    GCP project ID (required for GCP)"
  echo "  --domain=DOMAIN_NAME       Domain name for ingress (required for GCP)"
  echo ""
  echo "Examples:"
  echo "  $0 local                   Setup for local development"
  echo "  $0 gcp --project-id=my-project --domain=example.com   Setup for GCP deployment"
  exit 1
}

# Parse command line arguments
for arg in "$@"; do
  case $arg in
    local|gcp)
      ENV=$arg
      ;;
    --project-id=*)
      PROJECT_ID="${arg#*=}"
      ;;
    --domain=*)
      DOMAIN_NAME="${arg#*=}"
      ;;
    --help|-h)
      usage
      ;;
  esac
done

# Validate arguments for GCP deployment
if [ "$ENV" = "gcp" ]; then
  if [ -z "$PROJECT_ID" ]; then
    echo "Error: --project-id is required for GCP deployment"
    usage
  fi
  if [ -z "$DOMAIN_NAME" ]; then
    echo "Error: --domain is required for GCP deployment"
    usage
  fi
fi

# Function to setup local development environment
setup_local() {
  echo "Setting up local development environment..."
  
  # Install dependencies
  npm install
  
  # Check if PostgreSQL is running
  if command -v pg_isready > /dev/null; then
    if pg_isready -h localhost -p 5432 > /dev/null 2>&1; then
      echo "PostgreSQL is running"
    else
      echo "Warning: PostgreSQL is not running. Please start PostgreSQL."
    fi
  else
    echo "Warning: pg_isready command not found. Cannot check PostgreSQL status."
  fi
  
  # Create database if it doesn't exist
  if command -v psql > /dev/null; then
    if ! psql -h localhost -U postgres -lqt | cut -d \| -f 1 | grep -qw tiptop; then
      echo "Creating tiptop database..."
      psql -h localhost -U postgres -c "CREATE DATABASE tiptop;"
    else
      echo "Database 'tiptop' already exists"
    fi
  else
    echo "Warning: psql command not found. Cannot create database."
    echo "Please create a database named 'tiptop' manually."
  fi
  
  echo "Local setup complete!"
  echo "You can start the server with: npm run dev"
}

# Function to setup GCP deployment
setup_gcp() {
  echo "Setting up GCP deployment..."
  
  # Update Kubernetes configuration files with project ID and domain
  sed -i "s/PROJECT_ID/$PROJECT_ID/g" k8s/deployment.yaml
  sed -i "s/DOMAIN_NAME/$DOMAIN_NAME/g" k8s/ingress.yaml
  
  # Build and push Docker image
  echo "Building Docker image..."
  docker build -t gcr.io/$PROJECT_ID/tiptop-websocket:latest .
  
  echo "Pushing Docker image to Google Container Registry..."
  docker push gcr.io/$PROJECT_ID/tiptop-websocket:latest
  
  # Apply Kubernetes configurations
  echo "Applying Kubernetes configurations..."
  kubectl apply -f k8s/deployment.yaml
  kubectl apply -f k8s/service.yaml
  kubectl apply -f k8s/ingress.yaml
  
  echo "GCP setup complete!"
  echo "You can check the deployment status with: kubectl get pods -n tiptop"
}

# Main execution
if [ "$ENV" = "local" ]; then
  setup_local
elif [ "$ENV" = "gcp" ]; then
  setup_gcp
else
  echo "Error: Invalid environment '$ENV'"
  usage
fi
