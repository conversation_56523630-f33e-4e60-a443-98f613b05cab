const WebSocket = require('ws');

// URL to connect to
const url = 'ws://localhost:8080?url=https://example.com';

// Create WebSocket connection
const socket = new WebSocket(url);

// Connection opened
socket.on('open', () => {
  console.log('Connected to WebSocket server');
  
  // Send presence message
  const presenceMessage = {
    type: 'presence',
    userId: 'test-user-id-cli',
    userName: 'CLI Test User',
    url: 'https://example.com',
    timestamp: new Date().toISOString()
  };
  
  socket.send(JSON.stringify(presenceMessage));
  console.log('Sent presence message');
  
  // Send chat message
  setTimeout(() => {
    const chatMessage = {
      type: 'chat',
      content: 'Hello from CLI test client',
      userId: 'test-user-id-cli',
      userName: 'CLI Test User',
      url: 'https://example.com',
      messageId: `cli-msg-${Date.now()}`,
      timestamp: new Date().toISOString()
    };
    
    socket.send(JSON.stringify(chatMessage));
    console.log('Sent chat message');
  }, 1000);
});

// Listen for messages
socket.on('message', (data) => {
  const message = JSON.parse(data);
  console.log('Received message:', message);
  
  // If we receive a history message, log the messages
  if (message.type === 'history') {
    console.log(`Received ${message.messages.length} history messages:`);
    message.messages.forEach((msg, index) => {
      console.log(`  ${index + 1}. ${msg.userName}: ${msg.content}`);
    });
  }
  
  // If we receive a users message, log the users
  if (message.type === 'users') {
    console.log(`Received ${message.users.length} users:`);
    message.users.forEach((user, index) => {
      console.log(`  ${index + 1}. ${user.userName} (${user.userId})`);
    });
  }
});

// Connection closed
socket.on('close', () => {
  console.log('Disconnected from WebSocket server');
});

// Connection error
socket.on('error', (error) => {
  console.error('WebSocket error:', error);
});

// Keep the process running
setTimeout(() => {
  console.log('Test complete');
  socket.close();
  process.exit(0);
}, 10000);
