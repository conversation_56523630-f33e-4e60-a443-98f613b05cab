/**
 * This script provides the code changes needed to update the TipTop extension
 * to use the WebSocket server for real-time communication.
 *
 * Instructions:
 * 1. Copy the connectWebSocket function to replace the existing one in social-client.js
 * 2. Copy the sendChatMessage function to replace the existing one in social-client.js
 * 3. <PERSON>py the handleChatMessage function to replace the existing one in social-client.js
 * 4. <PERSON>py the handlePresenceMessage function to replace the existing one in social-client.js
 * 5. <PERSON>py the handleHistoryChatMessage function to add to social-client.js
 * 6. Copy the handleHistoryNoteMessage function to add to social-client.js
 * 7. Copy the saveMessageAsNote function to replace the existing one in social-client.js
 * 8. Copy the updateActiveUsers function to add to social-client.js
 * 9. Copy the saveMessageLocally function to add to social-client.js
 * 10. Update the manifest.json file to allow WebSocket connections
 *
 * Important: The history message handling is crucial for ensuring new users see previous messages!
 */

/**
 * Replace the existing connectWebSocket function in social-client.js with this one
 */
function connectWebSocket() {
  if (socket && socket.readyState === WebSocket.OPEN) {
    console.log('WebSocket already connected');
    return;
  }

  // If we're in mock mode, use the mock WebSocket
  if (useMockWebSocket) {
    console.log('Using mock WebSocket');
    setupMockWebSocket();
    return;
  }

  // Encode the current URL to pass as a parameter
  const encodedUrl = encodeURIComponent(window.location.href);

  // Use the WebSocket server URL - update this to your actual server URL
  // For local development: ws://localhost:8080
  // For production: wss://ws.tiptop.yourdomain.com
  const wsUrl = `wss://ws.tiptop.yourdomain.com?url=${encodedUrl}`;

  console.log('Connecting to WebSocket server:', wsUrl);

  try {
    socket = new WebSocket(wsUrl);

    socket.onopen = function() {
      console.log('WebSocket connection established');
      updateConnectionStatus(true);

      // Send presence information
      socket.send(JSON.stringify({
        type: 'presence',
        userId: userId,
        userName: userName,
        url: window.location.href,
        timestamp: new Date().toISOString()
      }));
    };

    socket.onmessage = function(event) {
      console.log('WebSocket message received:', event.data);

      try {
        const message = JSON.parse(event.data);

        // Handle different message types
        switch (message.type) {
          case 'chat':
            handleChatMessage(message);
            break;
          case 'presence':
            handlePresenceMessage(message);
            break;
          case 'note':
            handleNoteMessage(message);
            break;
          case 'system':
            console.log('System message:', message.content);
            break;
          case 'history':
            if (message.messages && Array.isArray(message.messages)) {
              console.log(`Received history with ${message.messages.length} messages`);

              // Process history messages
              message.messages.forEach(msg => {
                if (msg.type === 'chat') {
                  // Use a special handler for history messages to avoid skipping them
                  handleHistoryChatMessage(msg);
                } else if (msg.type === 'note') {
                  // Use a special handler for history notes
                  handleHistoryNoteMessage(msg);
                }
              });

              console.log('Finished processing history messages');
            }
            break;
          case 'users':
            if (message.users && Array.isArray(message.users)) {
              updateActiveUsers(message.users);
            }
            break;
          default:
            console.log('Unknown message type:', message.type);
        }
      } catch (e) {
        console.error('Error processing WebSocket message:', e);
      }
    };

    socket.onclose = function(event) {
      console.log('WebSocket connection closed:', event.code, event.reason);
      updateConnectionStatus(false);

      // Try to reconnect after a delay
      setTimeout(connectWebSocket, 5000);
    };

    socket.onerror = function(error) {
      console.error('WebSocket error:', error);
      updateConnectionStatus(false);

      // Fall back to mock mode
      useMockWebSocket = true;
      setupMockWebSocket();
    };
  } catch (e) {
    console.error('Error creating WebSocket connection:', e);
    updateConnectionStatus(false);

    // Fall back to mock mode
    useMockWebSocket = true;
    setupMockWebSocket();
  }
}

/**
 * Replace the existing sendChatMessage function in social-client.js with this one
 */
function sendChatMessage(message) {
  if (!message || !socialEnabled) return false;

  // Generate a unique message ID
  const messageId = `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  const timestamp = new Date().toISOString();

  // Create the message object
  const messageObj = {
    type: 'chat',
    content: message,
    userId: userId,
    userName: userName,
    timestamp: timestamp,
    messageId: messageId,
    url: window.location.href
  };

  // If we have a real WebSocket connection, send through it
  if (socket && socket.readyState === WebSocket.OPEN && !useMockWebSocket) {
    try {
      socket.send(JSON.stringify(messageObj));

      // Add to UI immediately (own message)
      addChatMessage(userId, userName, message, timestamp, true, messageId);

      // Also save locally as backup
      saveMessageLocally(messageObj);

      return true;
    } catch (e) {
      console.error('Error sending WebSocket message:', e);
    }
  }

  // Fall back to mock mode if no WebSocket
  if (useMockWebSocket) {
    console.log('Using mock WebSocket to send message');

    // Add to UI immediately (own message)
    addChatMessage(userId, userName, message, timestamp, true, messageId);

    // Save to storage for cross-browser communication
    storeSharedMessage(message, userId, userName, timestamp, messageId);

    // Save message as note for history
    saveMessageAsNote(userId, userName, message, timestamp, messageId);

    // Broadcast to other tabs
    chrome.runtime.sendMessage({
      type: 'TIPTOP_CHAT_MESSAGE',
      data: messageObj
    });

    return true;
  }

  return false;
}

/**
 * Replace the existing handleChatMessage function in social-client.js with this one
 */
function handleChatMessage(message) {
  // Skip our own messages (we already added them when sending)
  if (message.userId === userId) {
    return;
  }

  // Skip messages we've already processed
  if (processedMessageIds.has(message.messageId)) {
    return;
  }

  console.log('Processing chat message:', message);

  // Add the message to the UI
  addChatMessage(
    message.userId,
    message.userName,
    message.content,
    message.timestamp,
    false, // not own message
    message.messageId
  );

  // Save message as note for history
  saveMessageAsNote(
    message.userId,
    message.userName,
    message.content,
    message.timestamp,
    message.messageId
  );
}

/**
 * Replace the existing handlePresenceMessage function in social-client.js with this one
 * or add it if it doesn't exist
 */
function handlePresenceMessage(message) {
  // Skip our own presence messages
  if (message.userId === userId) {
    return;
  }

  console.log('Processing presence message:', message);

  // Update active users list
  if (activeUsers) {
    // Check if user already exists
    const existingUserIndex = activeUsers.findIndex(u => u.userId === message.userId);

    if (existingUserIndex >= 0) {
      // Update existing user
      activeUsers[existingUserIndex] = {
        userId: message.userId,
        userName: message.userName,
        lastSeen: new Date().getTime()
      };
    } else {
      // Add new user
      activeUsers.push({
        userId: message.userId,
        userName: message.userName,
        lastSeen: new Date().getTime()
      });
    }

    // Update UI
    updateActiveUsersUI();
  }
}

/**
 * Add this function to social-client.js to update the active users list
 */
function updateActiveUsers(users) {
  if (!Array.isArray(users)) return;

  // Update active users list
  activeUsers = users.map(user => ({
    userId: user.userId,
    userName: user.userName,
    lastSeen: new Date().getTime(),
    status: 'online' // All users in the list are online
  }));

  // Update UI
  updateActiveUsersUI();
}

/**
 * Add this CSS to your extension's styles for user status badges
 */
/*
.user-item {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
}

.user-status-badge {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 5px;
}

.user-status-badge.online {
  background-color: #2BAC76; /* Slack-like green */
}

.user-status-badge.offline {
  background-color: #E8E8E8;
  border: 1px solid #CCCCCC;
}
*/

/**
 * Add or update this function in social-client.js to render users with status badges
 */
function updateActiveUsersUI() {
  const usersContainer = document.getElementById('tiptop-users-container');
  if (!usersContainer) return;

  // Clear existing users
  usersContainer.innerHTML = '';

  if (!activeUsers || activeUsers.length === 0) {
    usersContainer.innerHTML = '<div class="tiptop-no-users">No other users viewing this page</div>';
    return;
  }

  // Add each user with status badge
  activeUsers.forEach(user => {
    const userElement = document.createElement('div');
    userElement.className = 'tiptop-user-item';

    // Create status badge
    const statusBadge = document.createElement('span');
    statusBadge.className = `tiptop-user-status-badge ${user.status || 'offline'}`;
    userElement.appendChild(statusBadge);

    // Create user name
    const userName = document.createElement('span');
    userName.className = 'tiptop-user-name';
    userName.textContent = user.userName;
    userElement.appendChild(userName);

    // Highlight current user
    if (user.userId === userId) {
      userElement.classList.add('tiptop-current-user');
    }

    usersContainer.appendChild(userElement);
  });
}

/**
 * Add this function to social-client.js to handle history chat messages
 * This is a special version that bypasses the processedMessageIds check
 */
function handleHistoryChatMessage(message) {
  // Skip our own messages
  if (message.userId === userId) {
    return;
  }

  console.log('Processing history chat message:', message);

  // Add the message to the UI even if we've seen it before
  // This ensures history messages are always displayed
  addChatMessage(
    message.userId,
    message.userName,
    message.content,
    message.timestamp,
    false, // not own message
    message.messageId
  );

  // Mark as processed to avoid duplicates from real-time messages
  processedMessageIds.add(message.messageId);
}

/**
 * Add this function to social-client.js to handle history note messages
 * This is a special version that bypasses the usual checks
 */
function handleHistoryNoteMessage(message) {
  console.log('Processing history note message:', message);

  // Process the note even if we've seen it before
  // This ensures history notes are always displayed
  saveMessageAsNote(
    message.userId,
    message.userName,
    message.content,
    message.timestamp,
    message.messageId,
    true // isHistory flag to bypass duplicate checks
  );
}

/**
 * Add this function to social-client.js to save messages locally as backup
 */
function saveMessageLocally(messageObj) {
  const currentUrl = messageObj.url;
  const urlKey = btoa(currentUrl).replace(/\+/g, '-').replace(/\//g, '_').replace(/=+$/, '');
  const messagesKey = `tiptop_msgs_${urlKey}`;

  chrome.storage.local.get([messagesKey], function(result) {
    let urlMessages = result[messagesKey] || [];

    // Check if this message already exists
    const existingIndex = urlMessages.findIndex(m => m.messageId === messageObj.messageId);
    if (existingIndex >= 0) {
      return;
    }

    // Add the new message
    urlMessages.push(messageObj);

    // Limit to 50 messages
    if (urlMessages.length > 50) {
      urlMessages = urlMessages.slice(-50);
    }

    // Save back to storage
    const storageData = {};
    storageData[messagesKey] = urlMessages;

    chrome.storage.local.set(storageData);
  });
}

/**
 * Replace the existing saveMessageAsNote function in social-client.js with this one
 */
function saveMessageAsNote(senderId, senderName, message, timestamp, messageId, isHistory = false) {
  // Skip if we're in mock mode and this is a message from the current user
  // This prevents duplicate messages in the chat history
  if (!isHistory && useMockWebSocket && senderId === userId) {
    console.log('Skipping saving own message as note in mock mode to prevent duplicates');
    return;
  }

  // Create a note object from the chat message
  const note = {
    url: window.location.href,
    title: document.title,
    text: message,
    userName: senderName || 'Unknown User',
    timestamp: timestamp || new Date().toISOString(),
    noteId: `chat_${messageId}`,
    userId: senderId,
    type: 'chat' // Mark this as a chat message
  };

  // Create a URL-safe key for the current URL
  const currentUrl = note.url;
  const urlKey = btoa(currentUrl).replace(/\+/g, '-').replace(/\//g, '_').replace(/=+$/, '');
  const notesKey = `tiptop_notes_${urlKey}`;

  // Store the note in local storage
  chrome.storage.local.get([notesKey], function(result) {
    let urlNotes = result[notesKey] || [];

    // Check if we already have this note (by ID)
    const existingNoteIndex = urlNotes.findIndex(n => n.noteId === note.noteId);

    // If this is a history message, we always want to process it
    // If it's a regular message, we only process it if it's new
    if (existingNoteIndex >= 0 && !isHistory) {
      console.log('Note already exists, not adding again:', note.noteId);
      return;
    }

    if (existingNoteIndex >= 0) {
      // Update existing note
      urlNotes[existingNoteIndex] = note;
    } else {
      // Add new note
      urlNotes.push(note);

      // Limit to 50 notes per URL
      if (urlNotes.length > 50) {
        urlNotes = urlNotes.slice(-50);
      }
    }

    // Save updated notes
    const storageData = {};
    storageData[notesKey] = urlNotes;

    chrome.storage.local.set(storageData, function() {
      // If we're currently on this URL, refresh the history display
      if (window.location.href === currentUrl) {
        // Dispatch a custom event that content.js can listen for
        document.dispatchEvent(new CustomEvent('tiptop-notes-updated'));
      }
    });
  });
}

/**
 * Update your manifest.json file to allow WebSocket connections
 * Add or update the content_security_policy section:
 */
/*
"content_security_policy": {
  "extension_pages": "script-src 'self'; object-src 'self'; connect-src 'self' http://localhost:30080 ws://localhost:30080 ws://localhost:30081 wss://ws.tiptop.yourdomain.com;"
}
*/
