<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TipTop WebSocket Test Client</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            display: flex;
            flex-direction: column;
            height: 100vh;
        }
        .connection-status {
            padding: 10px;
            margin-bottom: 10px;
            border-radius: 4px;
        }
        .connected {
            background-color: #d4edda;
            color: #155724;
        }
        .disconnected {
            background-color: #f8d7da;
            color: #721c24;
        }
        .chat-container {
            display: flex;
            flex-direction: column;
            flex-grow: 1;
            border: 1px solid #ccc;
            border-radius: 4px;
            overflow: hidden;
        }
        .chat-messages {
            flex-grow: 1;
            padding: 10px;
            overflow-y: auto;
            background-color: #f8f9fa;
        }
        .message {
            margin-bottom: 10px;
            padding: 8px 12px;
            border-radius: 4px;
            max-width: 80%;
        }
        .message.own {
            background-color: #d1ecf1;
            color: #0c5460;
            align-self: flex-end;
            margin-left: auto;
        }
        .message.other {
            background-color: #e2e3e5;
            color: #383d41;
        }
        .message.system {
            background-color: #fff3cd;
            color: #856404;
            width: 100%;
            text-align: center;
            font-style: italic;
        }
        .message-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
            font-size: 0.8em;
            color: #6c757d;
        }
        .message-content {
            word-break: break-word;
        }
        .chat-input {
            display: flex;
            padding: 10px;
            background-color: #f8f9fa;
            border-top: 1px solid #ccc;
        }
        .chat-input input {
            flex-grow: 1;
            padding: 8px;
            border: 1px solid #ccc;
            border-radius: 4px;
            margin-right: 10px;
        }
        .chat-input button {
            padding: 8px 16px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .chat-input button:hover {
            background-color: #0069d9;
        }
        .user-list {
            margin-bottom: 10px;
            padding: 10px;
            background-color: #f8f9fa;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
        .user-list h3 {
            margin-top: 0;
        }
        .user {
            display: inline-flex;
            align-items: center;
            margin-right: 10px;
            margin-bottom: 5px;
            padding: 5px 10px;
            background-color: #e2e3e5;
            border-radius: 15px;
            font-size: 0.9em;
        }
        .user-status {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 5px;
        }
        .user-status.online {
            background-color: #2BAC76; /* Slack-like green */
        }
        .user-status.offline {
            background-color: #E8E8E8; /* Light gray */
            border: 1px solid #CCCCCC;
        }
        .controls {
            margin-bottom: 10px;
        }
        .controls input {
            padding: 8px;
            border: 1px solid #ccc;
            border-radius: 4px;
            margin-right: 10px;
            width: 300px;
        }
        .controls button {
            padding: 8px 16px;
            background-color: #28a745;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .controls button:hover {
            background-color: #218838;
        }
        .controls button.disconnect {
            background-color: #dc3545;
        }
        .controls button.disconnect:hover {
            background-color: #c82333;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>TipTop WebSocket Test Client</h1>

        <div class="controls">
            <input type="text" id="server-url" placeholder="WebSocket Server URL (ws://localhost:8080)" value="wss://ws.tiptop.qubitrhythm.com">
            <input type="text" id="page-url" placeholder="Page URL (https://example.com)" value="https://example.com">
            <input type="text" id="user-name" placeholder="Your Name" value="Test User">
            <button id="connect-btn">Connect</button>
            <button id="disconnect-btn" class="disconnect" disabled>Disconnect</button>
        </div>

        <div id="connection-status" class="connection-status disconnected">
            Disconnected
        </div>

        <div id="user-list" class="user-list" style="display: none;">
            <h3>Active Users</h3>
            <div id="users"></div>
        </div>

        <div class="chat-container">
            <div id="chat-messages" class="chat-messages"></div>

            <div class="chat-input">
                <input type="text" id="message-input" placeholder="Type a message..." disabled>
                <button id="send-btn" disabled>Send</button>
            </div>
        </div>
    </div>

    <script>
        // DOM elements
        const serverUrlInput = document.getElementById('server-url');
        const pageUrlInput = document.getElementById('page-url');
        const userNameInput = document.getElementById('user-name');
        const connectBtn = document.getElementById('connect-btn');
        const disconnectBtn = document.getElementById('disconnect-btn');
        const connectionStatus = document.getElementById('connection-status');
        const userList = document.getElementById('user-list');
        const usersContainer = document.getElementById('users');
        const chatMessages = document.getElementById('chat-messages');
        const messageInput = document.getElementById('message-input');
        const sendBtn = document.getElementById('send-btn');

        // WebSocket connection
        let socket = null;
        let userId = 'user_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);

        // Connect to WebSocket server
        connectBtn.addEventListener('click', () => {
            const serverUrl = serverUrlInput.value;
            const pageUrl = encodeURIComponent(pageUrlInput.value);
            const userName = userNameInput.value;

            if (!serverUrl || !pageUrl || !userName) {
                alert('Please fill in all fields');
                return;
            }

            console.log('Connecting to WebSocket server with URL:', pageUrlInput.value);
            console.log('Encoded URL parameter:', pageUrl);

            // Create WebSocket connection
            try {
                const wsUrl = `${serverUrl}?url=${pageUrl}`;
                console.log('Full WebSocket URL:', wsUrl);
                socket = new WebSocket(wsUrl);

                // Connection opened
                socket.addEventListener('open', () => {
                    connectionStatus.textContent = 'Connected';
                    connectionStatus.classList.remove('disconnected');
                    connectionStatus.classList.add('connected');

                    connectBtn.disabled = true;
                    disconnectBtn.disabled = false;
                    messageInput.disabled = false;
                    sendBtn.disabled = false;
                    userList.style.display = 'block';

                    // Send presence message
                    sendPresence(userName);
                });

                // Listen for messages
                socket.addEventListener('message', (event) => {
                    const message = JSON.parse(event.data);
                    console.log('Message from server:', message);

                    // Handle different message types
                    switch (message.type) {
                        case 'chat':
                            addChatMessage(message);
                            break;
                        case 'system':
                            addSystemMessage(message.content);
                            break;
                        case 'users':
                            updateUserList(message.users);
                            break;
                        case 'history':
                            console.log('Received history message:', message);
                            if (message.messages && Array.isArray(message.messages)) {
                                console.log(`Processing ${message.messages.length} history messages`);

                                // Clear existing messages when receiving history
                                chatMessages.innerHTML = '';

                                // No system message for history loading

                                // Add messages in chronological order (oldest first)
                                message.messages.forEach((msg, index) => {
                                    console.log(`Processing history message ${index + 1}/${message.messages.length}:`, msg);
                                    if (msg.type === 'chat') {
                                        try {
                                            addChatMessage(msg);
                                            console.log(`Added history message ${index + 1} to UI`);
                                        } catch (err) {
                                            console.error(`Error adding history message ${index + 1} to UI:`, err);
                                        }
                                    } else {
                                        console.log(`Skipping non-chat message of type: ${msg.type}`);
                                    }
                                });

                                // No system message for history completion
                                console.log('Finished processing history messages');
                            } else {
                                console.error('Invalid history message format:', message);
                            }
                            break;
                    }
                });

                // Connection closed
                socket.addEventListener('close', () => {
                    connectionStatus.textContent = 'Disconnected';
                    connectionStatus.classList.remove('connected');
                    connectionStatus.classList.add('disconnected');

                    connectBtn.disabled = false;
                    disconnectBtn.disabled = true;
                    messageInput.disabled = true;
                    sendBtn.disabled = true;
                    userList.style.display = 'none';
                });

                // Connection error
                socket.addEventListener('error', (error) => {
                    console.error('WebSocket error:', error);
                    // No system message for errors
                });
            } catch (error) {
                console.error('Error creating WebSocket connection:', error);
                alert('Error creating WebSocket connection: ' + error.message);
            }
        });

        // Disconnect from WebSocket server
        disconnectBtn.addEventListener('click', () => {
            if (socket) {
                socket.close();
                socket = null;
            }
        });

        // Send message
        sendBtn.addEventListener('click', sendMessage);
        messageInput.addEventListener('keypress', (event) => {
            if (event.key === 'Enter') {
                sendMessage();
            }
        });

        function sendMessage() {
            const message = messageInput.value.trim();
            if (!message) return;

            if (socket && socket.readyState === WebSocket.OPEN) {
                // Generate a unique message ID
                const messageId = 'msg_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);

                // Create message object
                const messageObj = {
                    type: 'chat',
                    content: message,
                    userId: userId,
                    userName: userNameInput.value,
                    url: pageUrlInput.value, // Use the unencoded URL
                    timestamp: new Date().toISOString(),
                    messageId: messageId
                };

                console.log('Sending chat message with URL:', pageUrlInput.value);

                // Send message
                socket.send(JSON.stringify(messageObj));

                // Clear input
                messageInput.value = '';
            }
        }

        // Send presence message
        function sendPresence(userName) {
            if (socket && socket.readyState === WebSocket.OPEN) {
                const presenceMessage = {
                    type: 'presence',
                    userId: userId,
                    userName: userName,
                    url: pageUrlInput.value, // Use the unencoded URL
                    timestamp: new Date().toISOString()
                };

                console.log('Sending presence message with URL:', pageUrlInput.value);
                console.log('Presence message:', presenceMessage);

                socket.send(JSON.stringify(presenceMessage));
            }
        }

        // Add chat message to UI
        function addChatMessage(message) {
            const messageElement = document.createElement('div');
            messageElement.classList.add('message');

            // Check if this is our own message
            if (message.userId === userId) {
                messageElement.classList.add('own');
            } else {
                messageElement.classList.add('other');
            }

            // Format timestamp
            const timestamp = new Date(message.timestamp);
            const formattedTime = timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });

            // Create message content
            messageElement.innerHTML = `
                <div class="message-header">
                    <span>${message.userName}</span>
                    <span>${formattedTime}</span>
                </div>
                <div class="message-content">${message.content}</div>
            `;

            // Add to chat container
            chatMessages.appendChild(messageElement);

            // Scroll to bottom
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        // Add system message to UI
        function addSystemMessage(content) {
            const messageElement = document.createElement('div');
            messageElement.classList.add('message', 'system');
            messageElement.textContent = content;

            // Add to chat container
            chatMessages.appendChild(messageElement);

            // Scroll to bottom
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        // Update user list
        function updateUserList(users) {
            if (!users || !Array.isArray(users)) return;

            // Clear current list
            usersContainer.innerHTML = '';

            // Add users
            users.forEach(user => {
                const userElement = document.createElement('div');
                userElement.classList.add('user');

                // Add status indicator
                const statusIndicator = document.createElement('span');
                statusIndicator.classList.add('user-status', 'online'); // All users in the list are online
                userElement.appendChild(statusIndicator);

                // Add user name
                const nameSpan = document.createElement('span');
                nameSpan.textContent = user.userName;
                userElement.appendChild(nameSpan);

                // Highlight current user
                if (user.userId === userId) {
                    userElement.style.backgroundColor = '#d1ecf1';
                }

                usersContainer.appendChild(userElement);
            });
        }
    </script>
</body>
</html>
