#!/bin/bash

# TipTop WebSocket Server Local Development Script
# This script sets up the WebSocket server for local development

# Set variables
DB_USER="postgres"
DB_PASSWORD="Happy4WS$"
DB_NAME="tiptop"
DB_HOST="localhost"
DB_PORT="5432"
DEBUG="true"

# Check if PostgreSQL is installed
if ! command -v psql &> /dev/null; then
  echo "Error: PostgreSQL client is not installed."
  echo "Please install PostgreSQL client before running this script."
  exit 1
fi

# Check if PostgreSQL is running
if ! pg_isready -h $DB_HOST -p $DB_PORT > /dev/null 2>&1; then
  echo "Error: PostgreSQL is not running on $DB_HOST:$DB_PORT."
  echo "Please start PostgreSQL before running this script."
  exit 1
fi

# Create database if it doesn't exist
if ! psql -h $DB_HOST -p $DB_PORT -U $DB_USER -lqt | cut -d \| -f 1 | grep -qw $DB_NAME; then
  echo "Creating database: $DB_NAME"
  psql -h $DB_HOST -p $DB_PORT -U $DB_USER -c "CREATE DATABASE $DB_NAME;"
fi

# Initialize database
echo "Initializing database..."
PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -f tiptop-websocket-db-init.sql

# Create .env file
echo "Creating .env file..."
cat > ../.env << EOF
# Server configuration
PORT=8080

# Database configuration
DB_USER=$DB_USER
DB_HOST=$DB_HOST
DB_NAME=$DB_NAME
DB_PASSWORD=$DB_PASSWORD
DB_PORT=$DB_PORT

# Set to true to enable detailed logging
DEBUG=$DEBUG
EOF

# Install dependencies
echo "Installing dependencies..."
cd ..
npm install

# Start the server
echo "Starting the WebSocket server..."
echo "The server will be available at: ws://localhost:8080"
echo "You can test it with the test client: test-client.html"
echo ""
echo "Press Ctrl+C to stop the server."
npm start
