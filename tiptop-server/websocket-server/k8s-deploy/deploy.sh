#!/bin/bash

# TipTop WebSocket Server Deployment Script
# This script deploys the WebSocket server to GCP Kubernetes

# Set variables
GCP_PROJECT_ID=${1:-"YOUR_GCP_PROJECT_ID"}
IMAGE_NAME="tiptop-websocket"
IMAGE_TAG="latest"

# Check if project ID is provided
if [ "$GCP_PROJECT_ID" == "YOUR_GCP_PROJECT_ID" ]; then
  echo "Error: Please provide your GCP project ID as the first argument."
  echo "Usage: ./deploy.sh YOUR_GCP_PROJECT_ID"
  exit 1
fi

# Update deployment file with project ID
echo "Updating deployment file with project ID: $GCP_PROJECT_ID"
sed -i "s/PROJECT_ID/$GCP_PROJECT_ID/g" tiptop-websocket-deployment.yaml

# Build the Docker image
echo "Building Docker image..."
cd ..
docker build -t gcr.io/$GCP_PROJECT_ID/$IMAGE_NAME:$IMAGE_TAG .
cd k8s-deploy

# Push the image to Google Container Registry
echo "Pushing Docker image to GCR..."
docker push gcr.io/$GCP_PROJECT_ID/$IMAGE_NAME:$IMAGE_TAG

# Apply Kubernetes configurations
echo "Applying Kubernetes configurations..."
kubectl apply -f tiptop-websocket-secrets.yaml
kubectl apply -f tiptop-websocket-db-init-configmap.yaml
kubectl apply -f tiptop-websocket-db-init-job.yaml
kubectl apply -f tiptop-websocket-deployment.yaml
kubectl apply -f tiptop-websocket-service.yaml
kubectl apply -f tiptop-websocket-ingress.yaml

# Wait for deployment to complete
echo "Waiting for deployment to complete..."
kubectl rollout status deployment/tiptop-websocket -n tiptop

echo "Deployment completed successfully!"
echo "WebSocket server should be accessible at: wss://ws.tiptop.qubitrhythm.com"
echo ""
echo "To check the status of the deployment, run:"
echo "kubectl get pods -n tiptop | grep websocket"
echo ""
echo "To check the logs of the WebSocket server, run:"
echo "kubectl logs -f deployment/tiptop-websocket -n tiptop"
