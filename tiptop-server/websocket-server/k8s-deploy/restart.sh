#!/bin/bash

# TipTop WebSocket Server Restart Script
# This script restarts the WebSocket server deployment

# Set variables
NAMESPACE="tiptop"
DEPLOYMENT="tiptop-websocket"

# Check if kubectl is installed
if ! command -v kubectl &> /dev/null; then
  echo "Error: kubectl is not installed."
  echo "Please install kubectl before running this script."
  exit 1
fi

# Check if the deployment exists
if ! kubectl get deployment $DEPLOYMENT -n $NAMESPACE &> /dev/null; then
  echo "Error: Deployment $DEPLOYMENT not found in namespace $NAMESPACE."
  exit 1
fi

# Restart the deployment
echo "Restarting WebSocket server deployment: $DEPLOYMENT in namespace $NAMESPACE"
kubectl rollout restart deployment $DEPLOYMENT -n $NAMESPACE

# Wait for the deployment to complete
echo "Waiting for deployment to complete..."
kubectl rollout status deployment/$DEPLOYMENT -n $NAMESPACE

echo "Restart completed successfully!"
echo ""
echo "To check the logs of the WebSocket server, run:"
echo "kubectl logs -f deployment/$DEPLOYMENT -n $NAMESPACE"
