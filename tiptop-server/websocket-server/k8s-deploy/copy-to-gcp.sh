#!/bin/bash

# TipTop WebSocket Server Copy to GCP Script
# This script copies all the necessary files to the remote GCP environment

# Set variables
GCP_PROJECT_ID=${1:-"YOUR_GCP_PROJECT_ID"}
GCP_ZONE=${2:-"us-central1-a"}
GCP_INSTANCE=${3:-"YOUR_GCP_INSTANCE_NAME"}

# Check if project ID is provided
if [ "$GCP_PROJECT_ID" == "YOUR_GCP_PROJECT_ID" ]; then
  echo "Error: Please provide your GCP project ID as the first argument."
  echo "Usage: ./copy-to-gcp.sh YOUR_GCP_PROJECT_ID [GCP_ZONE] [GCP_INSTANCE_NAME]"
  exit 1
fi

# Check if instance name is provided
if [ "$GCP_INSTANCE" == "YOUR_GCP_INSTANCE_NAME" ]; then
  echo "Error: Please provide your GCP instance name as the third argument."
  echo "Usage: ./copy-to-gcp.sh YOUR_GCP_PROJECT_ID [GCP_ZONE] [GCP_INSTANCE_NAME]"
  exit 1
fi

# Create a temporary directory
echo "Creating temporary directory..."
TEMP_DIR=$(mktemp -d)

# Copy all the necessary files to the temporary directory
echo "Copying files to temporary directory..."
mkdir -p $TEMP_DIR/tiptop-websocket-server
cp -r ../* $TEMP_DIR/tiptop-websocket-server/

# Create a tarball
echo "Creating tarball..."
cd $TEMP_DIR
tar -czf tiptop-websocket-server.tar.gz tiptop-websocket-server

# Copy the tarball to the GCP instance
echo "Copying tarball to GCP instance..."
gcloud compute scp tiptop-websocket-server.tar.gz $GCP_INSTANCE:~ --zone=$GCP_ZONE --project=$GCP_PROJECT_ID

# Extract the tarball on the GCP instance
echo "Extracting tarball on GCP instance..."
gcloud compute ssh $GCP_INSTANCE --zone=$GCP_ZONE --project=$GCP_PROJECT_ID --command="tar -xzf tiptop-websocket-server.tar.gz && rm tiptop-websocket-server.tar.gz"

# Clean up
echo "Cleaning up..."
rm -rf $TEMP_DIR

echo "Files copied successfully to GCP instance: $GCP_INSTANCE"
echo ""
echo "To deploy the WebSocket server on the GCP instance, run:"
echo "gcloud compute ssh $GCP_INSTANCE --zone=$GCP_ZONE --project=$GCP_PROJECT_ID --command=\"cd tiptop-websocket-server/k8s-deploy && ./deploy.sh $GCP_PROJECT_ID\""
