#!/bin/bash

# TipTop Extension Update Script
# This script updates the TipTop extension to use the WebSocket server

# Set variables
EXTENSION_DIR="../../../tiptop-extension"
WEBSOCKET_URL="wss://ws.tiptop.qubitrhythm.com"
LOCAL_WEBSOCKET_URL="ws://localhost:8080"

# Check if the extension directory exists
if [ ! -d "$EXTENSION_DIR" ]; then
  echo "Error: Extension directory not found: $EXTENSION_DIR"
  exit 1
fi

# Update the manifest.json file
echo "Updating manifest.json..."
sed -i 's|connect-src .self. http://localhost:30080 ws://localhost:30080 ws://localhost:30081;|connect-src .self. http://localhost:30080 ws://localhost:30080 ws://localhost:30081 wss://ws.tiptop.qubitrhythm.com;|g' "$EXTENSION_DIR/manifest.json"

# Update the social-client.js file
echo "Updating social-client.js..."
sed -i "s|const wsUrl = .*|const wsUrl = \`$WEBSOCKET_URL?url=\${encodedUrl}\`;|g" "$EXTENSION_DIR/social-client.js"

echo "Extension updated successfully!"
echo "The extension will now use the WebSocket server at: $WEBSOCKET_URL"
echo ""
echo "To use the local WebSocket server instead, run:"
echo "sed -i \"s|const wsUrl = .*|const wsUrl = \`$LOCAL_WEBSOCKET_URL?url=\${encodedUrl}\`;|g\" \"$EXTENSION_DIR/social-client.js\""
