#!/bin/bash

# TipTop WebSocket Server Monitor Script
# This script monitors the WebSocket server

# Set variables
NAMESPACE="tiptop"
DEPLOYMENT="tiptop-websocket"
POD_LABEL="app=tiptop-websocket"

# Check if kubectl is installed
if ! command -v kubectl &> /dev/null; then
  echo "Error: kubectl is not installed."
  echo "Please install kubectl before running this script."
  exit 1
fi

# Check if the deployment exists
if ! kubectl get deployment $DEPLOYMENT -n $NAMESPACE &> /dev/null; then
  echo "Error: Deployment $DEPLOYMENT not found in namespace $NAMESPACE."
  exit 1
fi

# Monitor the deployment
echo "Monitoring WebSocket server deployment: $DEPLOYMENT in namespace $NAMESPACE"
echo "Press Ctrl+C to stop monitoring."
echo ""

# Get the deployment status
kubectl get deployment $DEPLOYMENT -n $NAMESPACE -o wide

# Get the pods
echo ""
echo "Pods:"
kubectl get pods -n $NAMESPACE -l $POD_LABEL -o wide

# Get the service
echo ""
echo "Service:"
kubectl get service $DEPLOYMENT -n $NAMESPACE -o wide

# Get the ingress
echo ""
echo "Ingress:"
kubectl get ingress -n $NAMESPACE | grep $DEPLOYMENT

# Monitor the logs
echo ""
echo "Logs:"
kubectl logs -f deployment/$DEPLOYMENT -n $NAMESPACE
