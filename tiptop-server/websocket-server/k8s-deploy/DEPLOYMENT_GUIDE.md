# TipTop WebSocket Server Deployment Guide

This guide provides step-by-step instructions for deploying the TipTop WebSocket server to GCP using Docker, Kubernetes, and PostgreSQL.

## Prerequisites

- Google Cloud SDK installed and configured
- kubectl installed and configured to access your GCP Kubernetes cluster
- Docker installed
- Access to the TipTop GCP project

## Deployment Steps

### 1. <PERSON>lone the Repository

If you haven't already, clone the TipTop repository:

```bash
<NAME_EMAIL>:ermingpei/tiptop.git
cd tiptop
```

### 2. Configure the Deployment

Navigate to the deployment directory:

```bash
cd tiptop-websocket-server/k8s-deploy
```

Review and update the configuration files if needed:

- `tiptop-websocket-secrets.yaml`: Update the secrets if needed
- `tiptop-websocket-deployment.yaml`: Update the resource limits if needed
- `tiptop-websocket-ingress.yaml`: Update the host if needed

### 3. Deploy to GCP

Run the deployment script:

```bash
./deploy.sh YOUR_GCP_PROJECT_ID
```

This script will:
1. Build the Docker image
2. Push the image to Google Container Registry
3. Apply the Kubernetes configurations
4. Initialize the database
5. Deploy the WebSocket server

### 4. Verify the Deployment

Check the status of the deployment:

```bash
kubectl get pods -n tiptop | grep websocket
```

Check the logs of the WebSocket server:

```bash
kubectl logs -f deployment/tiptop-websocket -n tiptop
```

Check the status of the ingress:

```bash
kubectl get ingress -n tiptop
```

### 5. Update the Extension

Update the TipTop extension to use the WebSocket server:

```bash
./update-extension.sh
```

This script will update the manifest.json and social-client.js files to use the WebSocket server.

### 6. Test the WebSocket Server

You can test the WebSocket server using the test client:

```bash
open ../test-client.html
```

In the test client:
1. Set the WebSocket Server URL to: `wss://ws.tiptop.qubitrhythm.com`
2. Set the Page URL to any URL (e.g., `https://example.com`)
3. Set your name
4. Click "Connect"
5. Send a message

You should see the message appear in the chat.

## DNS Configuration

The WebSocket server will be accessible at `wss://ws.tiptop.qubitrhythm.com`. This subdomain needs to be configured in your DNS provider to point to your GCP Kubernetes cluster's ingress IP address.

To get the ingress IP address:

```bash
kubectl get ingress -n tiptop
```

Then, create an A record in your DNS provider:
- Name: `ws.tiptop.qubitrhythm.com`
- Type: A
- Value: [Ingress IP Address]

## Troubleshooting

### WebSocket Connection Issues

If you're having issues connecting to the WebSocket server:

1. Check if the WebSocket server is running:
   ```bash
   kubectl get pods -n tiptop | grep websocket
   ```

2. Check the logs of the WebSocket server:
   ```bash
   kubectl logs -f deployment/tiptop-websocket -n tiptop
   ```

3. Check if the ingress is properly configured:
   ```bash
   kubectl get ingress -n tiptop
   ```

4. Check if the DNS is properly configured:
   ```bash
   nslookup ws.tiptop.qubitrhythm.com
   ```

### Database Issues

If you're having issues with the database:

1. Check if the database initialization job completed successfully:
   ```bash
   kubectl get jobs -n tiptop | grep websocket-db-init
   ```

2. Check the logs of the database initialization job:
   ```bash
   kubectl logs -f job/tiptop-websocket-db-init-job -n tiptop
   ```

3. Check if the database is accessible from the WebSocket server:
   ```bash
   kubectl exec -it deployment/tiptop-websocket -n tiptop -- /bin/bash
   ping tiptop-postgres.tiptop.svc.cluster.local
   ```

## Maintenance

### Updating the WebSocket Server

To update the WebSocket server:

1. Make your changes to the server code
2. Run the deployment script again:
   ```bash
   ./deploy.sh YOUR_GCP_PROJECT_ID
   ```

### Scaling the WebSocket Server

To scale the WebSocket server:

```bash
kubectl scale deployment/tiptop-websocket -n tiptop --replicas=3
```

### Monitoring the WebSocket Server

You can monitor the WebSocket server using GCP's monitoring tools:

1. Go to the GCP Console
2. Navigate to Kubernetes Engine > Workloads
3. Find the tiptop-websocket deployment
4. Click on it to view its details and metrics

## Conclusion

You have successfully deployed the TipTop WebSocket server to GCP using Docker, Kubernetes, and PostgreSQL. The WebSocket server is now accessible at `wss://ws.tiptop.qubitrhythm.com` and can be used by the TipTop extension for real-time communication.
