#!/bin/bash

# TipTop WebSocket Server Test Script
# This script tests the WebSocket server

# Set variables
WEBSOCKET_URL=${1:-"ws://localhost:8080"}
PAGE_URL=${2:-"https://example.com"}
USER_NAME=${3:-"Test User"}

# Check if wscat is installed
if ! command -v wscat &> /dev/null; then
  echo "Error: wscat is not installed."
  echo "Please install wscat before running this script:"
  echo "npm install -g wscat"
  exit 1
fi

# Encode the page URL
ENCODED_PAGE_URL=$(echo -n "$PAGE_URL" | jq -s -R -r @uri)

# Connect to the WebSocket server
echo "Connecting to WebSocket server: $WEBSOCKET_URL?url=$ENCODED_PAGE_URL"
echo "Press Ctrl+C to disconnect."
echo ""

# Send a presence message
PRESENCE_MESSAGE="{\"type\":\"presence\",\"userId\":\"test-user-id\",\"userName\":\"$USER_NAME\",\"url\":\"$PAGE_URL\",\"timestamp\":\"$(date -u +"%Y-%m-%dT%H:%M:%S.%3NZ")\"}"
echo "Sending presence message: $PRESENCE_MESSAGE"

# Send a chat message
CHAT_MESSAGE="{\"type\":\"chat\",\"content\":\"Hello from test script\",\"userId\":\"test-user-id\",\"userName\":\"$USER_NAME\",\"url\":\"$PAGE_URL\",\"messageId\":\"test-msg-$(date +%s)\",\"timestamp\":\"$(date -u +"%Y-%m-%dT%H:%M:%S.%3NZ")\"}"
echo "Sending chat message: $CHAT_MESSAGE"

# Connect to the WebSocket server and send the messages
(echo "$PRESENCE_MESSAGE"; sleep 1; echo "$CHAT_MESSAGE"; cat) | wscat -c "$WEBSOCKET_URL?url=$ENCODED_PAGE_URL"
