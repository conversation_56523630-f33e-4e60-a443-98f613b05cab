apiVersion: apps/v1
kind: Deployment
metadata:
  name: tiptop-websocket
  namespace: tiptop
spec:
  replicas: 3
  selector:
    matchLabels:
      app: tiptop-websocket
  template:
    metadata:
      labels:
        app: tiptop-websocket
    spec:
      containers:
      - name: tiptop-websocket
        image: gcr.io/PROJECT_ID/tiptop-websocket:latest
        imagePullPolicy: Always
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        ports:
        - containerPort: 8080
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
        readinessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
        env:
        - name: PORT
          value: "8080"
        - name: DB_USER
          valueFrom:
            secretKeyRef:
              name: tiptop-websocket-secrets
              key: DB_USER
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: tiptop-websocket-secrets
              key: DB_PASSWORD
        - name: DB_NAME
          valueFrom:
            secretKeyRef:
              name: tiptop-websocket-secrets
              key: DB_NAME
        - name: DB_HOST
          valueFrom:
            secretKeyRef:
              name: tiptop-websocket-secrets
              key: DB_HOST
        - name: DB_PORT
          valueFrom:
            secretKeyRef:
              name: tiptop-websocket-secrets
              key: DB_PORT
        - name: DEBUG
          valueFrom:
            secretKeyRef:
              name: tiptop-websocket-secrets
              key: DEBUG
        # Database connection pool optimization
        - name: DB_POOL_SIZE
          value: "20"
        # Memory limits configuration
        - name: MAX_MESSAGES_PER_URL
          value: "100"
        - name: MAX_MESSAGE_SIZE_BYTES
          value: "10000"
        - name: MAX_URLS_STORED
          value: "1000"
        - name: MESSAGE_EXPIRY_MS
          value: "86400000"
        # Rate limiting configuration
        - name: RATE_LIMIT_MESSAGES_PER_MINUTE
          value: "30"
        - name: RATE_LIMIT_PRESENCE_PER_MINUTE
          value: "10"
