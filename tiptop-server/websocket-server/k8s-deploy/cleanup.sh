#!/bin/bash

# TipTop WebSocket Server Cleanup Script
# This script cleans up the WebSocket server deployment

# Set variables
NAMESPACE="tiptop"
DEPLOYMENT="tiptop-websocket"
SERVICE="tiptop-websocket"
INGRESS="tiptop-websocket-ingress"
SECRET="tiptop-websocket-secrets"
CONFIGMAP="tiptop-websocket-db-init"
JOB="tiptop-websocket-db-init-job"

# Check if kubectl is installed
if ! command -v kubectl &> /dev/null; then
  echo "Error: kubectl is not installed."
  echo "Please install kubectl before running this script."
  exit 1
fi

# Confirm cleanup
echo "This script will delete the following resources in namespace $NAMESPACE:"
echo "- Deployment: $DEPLOYMENT"
echo "- Service: $SERVICE"
echo "- Ingress: $INGRESS"
echo "- Secret: $SECRET"
echo "- ConfigMap: $CONFIGMAP"
echo "- Job: $JOB"
echo ""
read -p "Are you sure you want to continue? (y/n) " -n 1 -r
echo ""
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
  echo "Cleanup aborted."
  exit 1
fi

# Delete the resources
echo "Deleting resources..."
kubectl delete deployment $DEPLOYMENT -n $NAMESPACE
kubectl delete service $SERVICE -n $NAMESPACE
kubectl delete ingress $INGRESS -n $NAMESPACE
kubectl delete secret $SECRET -n $NAMESPACE
kubectl delete configmap $CONFIGMAP -n $NAMESPACE
kubectl delete job $JOB -n $NAMESPACE

echo "Cleanup completed successfully!"
