# TipTop WebSocket Server Deployment

This directory contains the Kubernetes configuration files and scripts for deploying the TipTop WebSocket server to GCP.

## Files

- `tiptop-websocket-secrets.yaml`: Kubernetes Secret for WebSocket server configuration
- `tiptop-websocket-deployment.yaml`: Kubernetes Deployment for the WebSocket server
- `tiptop-websocket-service.yaml`: Kubernetes Service for the WebSocket server
- `tiptop-websocket-ingress.yaml`: Kubernetes Ingress for the WebSocket server
- `tiptop-websocket-db-init.sql`: SQL script for initializing the database
- `tiptop-websocket-db-init-configmap.yaml`: ConfigMap for the database initialization script
- `tiptop-websocket-db-init-job.yaml`: Kubernetes Job for initializing the database
- `deploy.sh`: Script for deploying the WebSocket server to GCP
- `local-dev.sh`: Script for setting up local development environment

## Deployment to GCP

1. Make sure you have the Google Cloud SDK installed and configured:
   ```
   gcloud auth login
   gcloud config set project YOUR_GCP_PROJECT_ID
   ```

2. Make sure you have k<PERSON><PERSON>l configured to access your GCP Kubernetes cluster:
   ```
   gcloud container clusters get-credentials YOUR_CLUSTER_NAME --zone YOUR_ZONE --project YOUR_GCP_PROJECT_ID
   ```

3. Run the deployment script:
   ```
   ./deploy.sh YOUR_GCP_PROJECT_ID
   ```

4. The WebSocket server will be deployed to your GCP Kubernetes cluster and will be accessible at:
   ```
   wss://ws.tiptop.qubitrhythm.com
   ```

## Local Development

1. Make sure you have PostgreSQL installed and running locally.

2. Run the local development script:
   ```
   ./local-dev.sh
   ```

3. The WebSocket server will be started locally and will be accessible at:
   ```
   ws://localhost:8080
   ```

4. You can test it with the test client:
   ```
   open ../test-client.html
   ```

## Updating the Extension

To update the TipTop extension to use the WebSocket server, you need to:

1. Update the manifest.json file to allow WebSocket connections:
   ```json
   "content_security_policy": {
     "extension_pages": "script-src 'self'; object-src 'self'; connect-src 'self' wss://ws.tiptop.qubitrhythm.com;"
   }
   ```

2. Update the WebSocket connection URL in your extension's code:
   ```javascript
   // In social-client.js
   function connectWebSocket() {
     // ...
     const wsUrl = `wss://ws.tiptop.qubitrhythm.com?url=${encodedUrl}`;
     // ...
   }
   ```

## Troubleshooting

If you encounter any issues, you can check the logs of the WebSocket server:

```
kubectl logs -f deployment/tiptop-websocket -n tiptop
```

You can also check the status of the deployment:

```
kubectl get pods -n tiptop | grep websocket
```

If the WebSocket server is not accessible, you can check the status of the ingress:

```
kubectl get ingress -n tiptop
```
