#!/bin/bash

# <PERSON><PERSON>t to build and deploy the TipTop social features

# Set variables
NAMESPACE="tiptop"
IMAGE_NAME="cloud-function-tiptop:local"

echo "Building Docker image..."
cd cloud-function-tiptop
docker build -t $IMAGE_NAME .
cd ..

echo "Applying Kubernetes configurations..."
kubectl apply -f k8s-tiptop/tiptop-db-init-configmap.yaml
kubectl apply -f k8s-tiptop/cloud-function-tiptop-deployment.yaml
kubectl apply -f k8s-tiptop/tiptop-cloud-function-service.yaml

echo "Restarting the deployment..."
kubectl rollout restart deployment cloud-function-tiptop-deployment -n $NAMESPACE

echo "Waiting for deployment to complete..."
kubectl rollout status deployment cloud-function-tiptop-deployment -n $NAMESPACE

echo "Deployment complete!"
echo "You can now test the social features by loading the extension in Chrome."
echo "1. Open Chrome and go to chrome://extensions/"
echo "2. Enable Developer mode"
echo "3. Click 'Load unpacked' and select the tiptop-extension directory"
echo "4. Open two different browser windows and navigate to the same webpage"
echo "5. Click the TipTop button in both windows to open the panel"
echo "6. You should see each user appear in the other's social section"
echo "7. Try sending messages between the two instances"
