/**
 * Test script for TipTop WebSocket server
 * This script simulates multiple clients connecting to the WebSocket server.
 */

const WebSocket = require('ws');
const readline = require('readline');

// Configuration
const WS_URL = 'ws://localhost:30080/ws';
const NUM_CLIENTS = 2;
const TEST_URL = 'https://example.com/test-page';

// Create readline interface for user input
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Store client connections
const clients = [];

// Connect a client
function connectClient(clientId) {
  const userId = `test_user_${clientId}`;
  const userName = `Test User ${clientId}`;
  const url = `${WS_URL}?url=${encodeURIComponent(TEST_URL)}&userId=${userId}&userName=${encodeURIComponent(userName)}`;
  
  console.log(`Connecting client ${clientId} (${userName}) to ${url}`);
  
  const ws = new WebSocket(url);
  
  ws.on('open', () => {
    console.log(`Client ${clientId} connected`);
    clients.push({ id: clientId, userId, userName, ws });
    
    // If all clients are connected, start the test
    if (clients.length === NUM_CLIENTS) {
      startTest();
    }
  });
  
  ws.on('message', (data) => {
    try {
      const message = JSON.parse(data);
      console.log(`Client ${clientId} received: ${JSON.stringify(message, null, 2)}`);
    } catch (error) {
      console.error(`Error parsing message for client ${clientId}:`, error);
    }
  });
  
  ws.on('close', () => {
    console.log(`Client ${clientId} disconnected`);
  });
  
  ws.on('error', (error) => {
    console.error(`Client ${clientId} error:`, error);
  });
}

// Start the test
function startTest() {
  console.log('\nAll clients connected. Starting test...');
  console.log('Available commands:');
  console.log('  send <client_id> <message> - Send a message from a specific client');
  console.log('  disconnect <client_id> - Disconnect a specific client');
  console.log('  reconnect <client_id> - Reconnect a specific client');
  console.log('  exit - Exit the test\n');
  
  rl.on('line', (input) => {
    const args = input.trim().split(' ');
    const command = args[0];
    
    switch (command) {
      case 'send':
        if (args.length < 3) {
          console.log('Usage: send <client_id> <message>');
          return;
        }
        
        const clientId = parseInt(args[1]);
        const message = args.slice(2).join(' ');
        sendMessage(clientId, message);
        break;
        
      case 'disconnect':
        if (args.length < 2) {
          console.log('Usage: disconnect <client_id>');
          return;
        }
        
        disconnectClient(parseInt(args[1]));
        break;
        
      case 'reconnect':
        if (args.length < 2) {
          console.log('Usage: reconnect <client_id>');
          return;
        }
        
        reconnectClient(parseInt(args[1]));
        break;
        
      case 'exit':
        exitTest();
        break;
        
      default:
        console.log('Unknown command. Available commands: send, disconnect, reconnect, exit');
    }
  });
}

// Send a message from a client
function sendMessage(clientId, message) {
  const client = clients.find(c => c.id === clientId);
  
  if (!client) {
    console.log(`Client ${clientId} not found`);
    return;
  }
  
  if (client.ws.readyState !== WebSocket.OPEN) {
    console.log(`Client ${clientId} is not connected`);
    return;
  }
  
  client.ws.send(JSON.stringify({
    type: 'chat',
    content: message
  }));
  
  console.log(`Client ${clientId} sent: ${message}`);
}

// Disconnect a client
function disconnectClient(clientId) {
  const client = clients.find(c => c.id === clientId);
  
  if (!client) {
    console.log(`Client ${clientId} not found`);
    return;
  }
  
  client.ws.close();
  console.log(`Client ${clientId} disconnected`);
}

// Reconnect a client
function reconnectClient(clientId) {
  const client = clients.find(c => c.id === clientId);
  
  if (!client) {
    console.log(`Client ${clientId} not found`);
    return;
  }
  
  if (client.ws.readyState === WebSocket.OPEN) {
    console.log(`Client ${clientId} is already connected`);
    return;
  }
  
  connectClient(clientId);
}

// Exit the test
function exitTest() {
  console.log('Exiting test...');
  
  // Close all connections
  clients.forEach(client => {
    if (client.ws.readyState === WebSocket.OPEN) {
      client.ws.close();
    }
  });
  
  rl.close();
  process.exit(0);
}

// Start the test by connecting clients
console.log(`Starting test with ${NUM_CLIENTS} clients...`);
for (let i = 1; i <= NUM_CLIENTS; i++) {
  connectClient(i);
}
