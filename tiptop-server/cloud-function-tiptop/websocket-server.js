/**
 * WebSocket server for TipTop social features
 * This module handles real-time communication between TipTop users.
 */

const WebSocket = require('ws');
const url = require('url');
const { v4: uuidv4 } = require('uuid');
const config = require('./config');

// In-memory storage for active connections
// This will be lost on server restart - for production, consider using Redis
const connections = new Map(); // userId -> WebSocket
const urlGroups = new Map();   // pageUrl -> Set of userIds
const userInfo = new Map();    // userId -> { name, avatar, url, etc. }

// Initialize the WebSocket server
function initWebSocketServer(server) {
  console.log('Initializing WebSocket server for TipTop social features');

  // Create a WebSocket server without a path restriction
  const wss = new WebSocket.Server({
    server,
    // No path restriction to handle all WebSocket connections
  });

  // Log when the server is created
  console.log('WebSocket server created');

  wss.on('connection', (ws, req) => {
    console.log('WebSocket connection received:', req.url);

    // Only handle connections to /ws path
    if (req.url && req.url.startsWith('/ws')) {
      handleConnection(ws, req);
    } else {
      console.log('Rejecting WebSocket connection to non-/ws path:', req.url);
      ws.close(1003, 'Invalid path');
    }
  });

  // Heartbeat to keep connections alive and detect disconnections
  setInterval(() => {
    wss.clients.forEach(ws => {
      if (ws.isAlive === false) {
        // Connection is dead, handle disconnection
        handleDisconnection(ws);
        return ws.terminate();
      }

      ws.isAlive = false;
      ws.ping();
    });
  }, 30000); // Check every 30 seconds

  console.log('WebSocket server initialized');
  return wss;
}

// Handle new WebSocket connections
function handleConnection(ws, req) {
  // Parse URL parameters
  const parameters = url.parse(req.url, true).query;
  const pageUrl = parameters.url;
  let userId = parameters.userId;
  const userName = parameters.userName || 'Anonymous User';

  // Generate a userId if not provided
  if (!userId) {
    userId = uuidv4();
  }

  console.log(`WebSocket connection established for user ${userId} on page ${pageUrl}`);

  // Set up connection tracking
  ws.isAlive = true;
  ws.userId = userId;
  ws.pageUrl = pageUrl;

  // Store connection
  connections.set(userId, ws);

  // Store user info
  userInfo.set(userId, {
    name: userName,
    url: pageUrl,
    connectedAt: new Date().toISOString()
  });

  // Add user to URL group
  if (!urlGroups.has(pageUrl)) {
    urlGroups.set(pageUrl, new Set());
  }
  urlGroups.get(pageUrl).add(userId);

  // Set up heartbeat
  ws.on('pong', () => {
    ws.isAlive = true;
  });

  // Handle messages
  ws.on('message', (message) => {
    try {
      const data = JSON.parse(message);
      handleMessage(ws, data);
    } catch (error) {
      console.error('Error handling WebSocket message:', error);
      sendErrorToClient(ws, 'Invalid message format');
    }
  });

  // Handle disconnection
  ws.on('close', () => {
    handleDisconnection(ws);
  });

  // Send initial data to the client
  sendInitialData(ws);
}

// Handle WebSocket messages
function handleMessage(ws, data) {
  const { type, content } = data;
  const userId = ws.userId;
  const pageUrl = ws.pageUrl;

  console.log(`Received message of type ${type} from user ${userId}`);

  switch (type) {
    case 'chat':
      // Handle chat message
      broadcastToUrl(pageUrl, {
        type: 'chat',
        userId: userId,
        userName: userInfo.get(userId)?.name || 'Anonymous User',
        content: content,
        timestamp: new Date().toISOString()
      });

      // In a production environment, you would store the message in the database here
      break;

    case 'presence_update':
      // Update user presence information
      if (userInfo.has(userId)) {
        const info = userInfo.get(userId);
        userInfo.set(userId, { ...info, ...content });

        // Notify others of the update
        broadcastToUrl(pageUrl, {
          type: 'presence_update',
          userId: userId,
          updates: content
        }, userId); // Exclude the sender
      }
      break;

    default:
      console.warn(`Unknown message type: ${type}`);
      sendErrorToClient(ws, `Unknown message type: ${type}`);
  }
}

// Handle client disconnection
function handleDisconnection(ws) {
  const userId = ws.userId;
  const pageUrl = ws.pageUrl;

  if (!userId || !pageUrl) {
    return; // No valid user data
  }

  console.log(`WebSocket disconnected for user ${userId}`);

  // Remove from connections
  connections.delete(userId);

  // Remove from URL group
  if (urlGroups.has(pageUrl)) {
    urlGroups.get(pageUrl).delete(userId);

    // Clean up empty URL groups
    if (urlGroups.get(pageUrl).size === 0) {
      urlGroups.delete(pageUrl);
    } else {
      // Notify others in the group
      broadcastToUrl(pageUrl, {
        type: 'user_left',
        userId: userId,
        timestamp: new Date().toISOString()
      });
    }
  }

  // Update user info with disconnection time
  if (userInfo.has(userId)) {
    const info = userInfo.get(userId);
    userInfo.set(userId, {
      ...info,
      disconnectedAt: new Date().toISOString(),
      isActive: false
    });

    // In a production environment, you would update the database here
  }
}

// Send initial data to a newly connected client
function sendInitialData(ws) {
  const userId = ws.userId;
  const pageUrl = ws.pageUrl;

  // Get other users on the same page
  const otherUsers = [];
  if (urlGroups.has(pageUrl)) {
    urlGroups.get(pageUrl).forEach(id => {
      if (id !== userId && userInfo.has(id)) {
        otherUsers.push({
          userId: id,
          name: userInfo.get(id).name,
          connectedAt: userInfo.get(id).connectedAt
        });
      }
    });
  }

  // Send welcome message with user list
  sendToClient(ws, {
    type: 'welcome',
    userId: userId,
    users: otherUsers,
    timestamp: new Date().toISOString()
  });

  // Notify others that a new user has joined
  broadcastToUrl(pageUrl, {
    type: 'user_joined',
    userId: userId,
    name: userInfo.get(userId)?.name || 'Anonymous User',
    timestamp: new Date().toISOString()
  }, userId); // Exclude the sender
}

// Send a message to a specific client
function sendToClient(ws, data) {
  if (ws.readyState === WebSocket.OPEN) {
    ws.send(JSON.stringify(data));
  }
}

// Send an error message to a client
function sendErrorToClient(ws, errorMessage) {
  sendToClient(ws, {
    type: 'error',
    message: errorMessage,
    timestamp: new Date().toISOString()
  });
}

// Broadcast a message to all clients viewing a specific URL
function broadcastToUrl(pageUrl, data, excludeUserId = null) {
  if (!urlGroups.has(pageUrl)) {
    return; // No users on this URL
  }

  const message = JSON.stringify(data);

  urlGroups.get(pageUrl).forEach(userId => {
    if (userId !== excludeUserId && connections.has(userId)) {
      const ws = connections.get(userId);
      if (ws.readyState === WebSocket.OPEN) {
        ws.send(message);
      }
    }
  });
}

// Get statistics about current connections
function getStats() {
  return {
    totalConnections: connections.size,
    totalUrls: urlGroups.size,
    urlDistribution: Array.from(urlGroups.entries()).map(([url, users]) => ({
      url,
      userCount: users.size
    }))
  };
}

module.exports = {
  initWebSocketServer,
  getStats
};
