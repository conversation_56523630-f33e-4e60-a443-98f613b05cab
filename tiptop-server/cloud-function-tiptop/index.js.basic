// index.js

const express = require('express');
const path = require('path');
const axios = require('axios');
const cors = require('cors');
const functions = require('@google-cloud/functions-framework');
const morgan = require('morgan');
const stripeModule = require('stripe');
const crypto = require('crypto');
const nodemailer = require('nodemailer');
const { JSDOM } = require('jsdom');
const { Readability } = require('@mozilla/readability');
// Import new DB functions
const {
  pool,
  initializeDatabase,
  getInstallation,
  addInstallation,
  updateInstallationEmail,
  updateInstallationStatus,
  getInstallationsByEmail // Import the new function
} = require('./db');
require('dotenv').config();

// Status Constants
const STATUS_TRIAL_PENDING_EMAIL = 'TrialPendingEmail';
const STATUS_TRIAL_ACTIVE = 'TrialActive';
const STATUS_TRIAL_EXPIRED = 'TrialExpired';
const STATUS_SUBSCRIBED = 'Subscribed';
const TRIAL_PERIOD_MS = 7 * 24 * 60 * 60 * 1000; // 7 days

// Load configurations from .env
const SERVER_URL = process.env.SERVER_URL;
const config = {
  timeout: process.env.TIMEOUT || 8000, // Increased timeout to 8 seconds to match client
  grokApiUrl: process.env.GROK_API_URL,
  grokApiKey: process.env.GROK_API_KEY,
  stripeSecretKey: process.env.STRIPE_SECRET_KEY,
  stripeWebhookSecret: process.env.STRIPE_WEBHOOK_SECRET,
  port: process.env.PORT || 8080,
  planPriceIdsStandard: process.env.PLAN_PRICE_IDS_STANDARD,
  maxRetries: 2, // Add retry mechanism
  retryDelay: 1000, // 1 second delay between retries
};

// Validate essential environment variables
if (!config.stripeSecretKey) {
  throw new Error('STRIPE_SECRET_KEY environment variable is not set');
}
if (!config.stripeWebhookSecret) {
  throw new Error('STRIPE_WEBHOOK_SECRET environment variable is not set');
}
if (!SERVER_URL) {
  throw new Error('SERVER_URL environment variable is not set');
}
if (!config.grokApiUrl) {
  throw new Error('GROK_API_URL environment variable is not set');
}
if (!config.grokApiKey) {
  throw new Error('GROK_API_KEY environment variable is not set');
}

// Initialize Stripe
const stripe = stripeModule(config.stripeSecretKey);

// Initialize PostgreSQL database
initializeDatabase().catch(err => {
  console.error('Failed to initialize database:', err);
  process.exit(1);
});

const app = express();

// Security middleware to block common probe attempts
app.use((req, res, next) => {
  // List of paths commonly probed by attackers
  const suspiciousPaths = [
    '/.env',
    '/docker/.env',
    '/.git',
    '/wp-admin',
    '/wp-login',
    '/administrator',
    '/admin',
    '/backup',
    '/db',
    '/.DS_Store',
    '/config.php',
    '/phpinfo.php',
    '/phpmyadmin'
  ];

  const path = req.path.toLowerCase();
  if (suspiciousPaths.some(blockedPath => path.includes(blockedPath))) {
    // Log security probe attempt
    console.error(`Security probe attempt blocked: ${req.method} ${req.path} from ${req.ip}`);
    // Return 403 instead of 404 to indicate we actively block these requests
    return res.status(403).json({
      error: 'Access denied'
    });
  }

  next();
});

// Enhanced CORS Configuration
app.use(cors({
  origin: '*',
  methods: ['GET', 'POST', 'PUT', 'DELETE'],
  allowedHeaders: ['Content-Type', 'Authorization'],
  credentials: true
}));

// Add CORS pre-flight middleware
app.options('*', cors());

// Logging Middleware with enhanced health check filtering
app.use(morgan('combined', {
  skip: (req) => {
    // Skip logging for health check probes and /check endpoint
    const ua = (req.headers['user-agent'] || '').toLowerCase();
    const isHealthCheck = ua.includes('kube-probe') ||
                         req.path === '/check' ||
                         req.path === '/healthz' ||
                         req.path === '/ready';
    return isHealthCheck;
  }
}));

// Disable console.log for health check endpoints
const originalLog = console.log;
console.log = function() {
  const stack = new Error().stack;
  if (stack.includes('/check') || stack.includes('/healthz') || stack.includes('/ready')) {
    return;
  }
  originalLog.apply(console, arguments);
};

// Middleware to Parse JSON Bodies and Capture Raw Body for Webhooks
app.use(
  express.json({
    verify: (req, res, buf) => {
      req.rawBody = buf.toString();
    },
  })
);

// Serve Static Files
app.use(express.static(path.join(__dirname, 'public')));

// Serve staticHosting folder
app.use('/staticHosting', express.static(path.join(__dirname, 'public', 'staticHosting')));

// Root Route
app.get('/', (req, res) => {
  console.log('Received GET request at /');
  res.send('Server is running');
});

// Define PLAN_PRICE_IDS from environment variables
const PLAN_PRICE_IDS = {
  standard: config.planPriceIdsStandard,
};

// Verify Subscription Endpoint
app.post('/verify-subscription', async (req, res) => {
  try {
    const { email } = req.body;
    console.log('Verifying subscription for email:', email);

    if (!email) {
      return res.status(400).json({ error: 'Email is required.' });
    }

    // Check subscription status in Stripe
    const customers = await stripe.customers.list({
      email: email,
      limit: 1,
      expand: ['data.subscriptions']
    });

    let isSubscribed = false;
    let subscriptionDetails = null;

    if (customers.data.length > 0) {
      const customer = customers.data[0];
      console.log('Found customer:', customer.id);

      // Get all subscriptions for this customer
      const subscriptions = await stripe.subscriptions.list({
        customer: customer.id,
        status: 'all', // Check all statuses
        expand: ['data.plan']
      });

      console.log('Customer subscriptions:', subscriptions.data.map(sub => ({
        id: sub.id,
        status: sub.status,
        current_period_end: new Date(sub.current_period_end * 1000)
      })));

      // Check for an active or trialing subscription specifically
      const activeSubscription = subscriptions.data.find(
        sub => sub.status === 'active' || sub.status === 'trialing'
      );

      if (activeSubscription) {
        isSubscribed = true;
        subscriptionDetails = {
          status: activeSubscription.status,
          current_period_end: new Date(activeSubscription.current_period_end * 1000),
          plan: activeSubscription.plan?.nickname || 'Standard Plan'
        };
        console.log('Active subscription found:', subscriptionDetails);
      } else {
        console.log('No active or trialing subscription found for customer:', customer.id);
      }
    } else {
      console.log('No customer found for email:', email);
    }

    res.json({
      subscribed: isSubscribed,
      subscription: subscriptionDetails
    });
  } catch (error) {
    console.error('Error in /verify-subscription:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Endpoint to create a Stripe Checkout session
app.post('/create-checkout-session', async (req, res) => {
  try {
    const { email, plan } = req.body;

    if (!email || !plan) {
      return res.status(400).json({ error: 'Missing email or plan parameter' });
    }

    const priceId = PLAN_PRICE_IDS[plan];
    if (!priceId) {
      return res.status(400).json({ error: 'Invalid subscription plan selected' });
    }

    const session = await stripe.checkout.sessions.create({
      payment_method_types: ['card'],
      mode: 'subscription',
      customer_email: email,
      line_items: [
        {
          price: priceId,
          quantity: 1,
        },
      ],
      success_url: `${SERVER_URL}/staticHosting/success.html?session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: `${SERVER_URL}/staticHosting/cancel.html`,
      billing_address_collection: 'auto',
      metadata: {
        email: email,
        plan: plan,
      },
    });

    res.json({ url: session.url });
    console.log(`Checkout session created for ${email}, plan: ${plan}`);
  } catch (error) {
    console.error('Error in /create-checkout-session:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});


// --- Helper Function for Stripe Subscription Check ---
/**
 * Checks if an email has an active Stripe subscription.
 * Includes timeout handling.
 * @param {string} email - The customer's email address.
 * @returns {Promise<{isSubscribed: boolean, subscriptionDetails: object|null}>}
 */
async function checkSubscription(email) {
  if (!email) {
    return { isSubscribed: false, subscriptionDetails: null };
  }

  console.log(`Checking Stripe subscription for: ${email}`);
  try {
    // Timeout promise
    const timeoutPromise = new Promise((_, reject) =>
      setTimeout(() => reject(new Error('Stripe API timeout')), config.timeout || 10000) // Default 10s timeout
    );

    // Stripe API call promise
    const stripePromise = async () => {
      const customers = await stripe.customers.list({
        email: email,
        limit: 1,
      });

      if (customers.data.length === 0) {
        console.log(`Stripe: No customer found for ${email}`);
        return { isSubscribed: false, subscriptionDetails: null };
      }

      const customer = customers.data[0];
      const subscriptions = await stripe.subscriptions.list({
        customer: customer.id,
        status: 'all', // Fetch all to potentially log inactive ones if needed
        expand: ['data.plan'],
      });

      const activeSubscription = subscriptions.data.find(
        sub => sub.status === 'active' || sub.status === 'trialing'
      );

      if (activeSubscription) {
        const details = {
          status: activeSubscription.status,
          current_period_end: new Date(activeSubscription.current_period_end * 1000),
          plan: activeSubscription.plan?.nickname || 'Standard Plan'
        };
        console.log(`Stripe: Active subscription found for ${email}`, details);
        return { isSubscribed: true, subscriptionDetails: details };
      } else {
        console.log(`Stripe: No active/trialing subscription found for ${email}`);
        return { isSubscribed: false, subscriptionDetails: null };
      }
    };

    // Race the promises
    return await Promise.race([stripePromise(), timeoutPromise]);

  } catch (error) {
    console.error(`Error checking Stripe subscription for ${email}:`, error);
    // Don't block user flow on Stripe error, assume not subscribed
    return { isSubscribed: false, subscriptionDetails: null };
  }
}


// Stripe webhook endpoint
app.post('/webhook', async (req, res) => {
  const sig = req.headers['stripe-signature'];
  const endpointSecret = config.stripeWebhookSecret;

  let event;

  console.log('Received webhook:', req.rawBody);

  try {
    event = stripe.webhooks.constructEvent(req.rawBody, sig, endpointSecret);
    console.log('Webhook event constructed:', event.type);
  } catch (err) {
    console.error('Webhook signature verification failed:', err.message);
    return res.status(400).send(`Webhook Error: ${err.message}`);
  }

  // --- Helper function to update status for all installations linked to an email ---
  const updateStatusByEmail = async (email, newStatus) => {
    if (!email) {
      console.error(`Webhook: Cannot update status (${newStatus}) without an email.`);
      return;
    }
    try {
      const installations = await getInstallationsByEmail(email);
      if (installations.length === 0) {
        console.log(`Webhook: No installations found for email ${email} to update status to ${newStatus}.`);
        return;
      }
      console.log(`Webhook: Found ${installations.length} installation(s) for email ${email}. Updating status to ${newStatus}.`);
      // Update status for all found installations
      const updatePromises = installations.map(inst => updateInstallationStatus(inst.ip_hash, newStatus));
      await Promise.all(updatePromises);
      console.log(`Webhook: Successfully updated status to ${newStatus} for installations associated with ${email}.`);
    } catch (dbError) {
      console.error(`Webhook: Database error updating status to ${newStatus} for email ${email}:`, dbError);
      // Decide if we should throw or just log
    }
  };

  // --- Handle different event types ---
  let customerEmail = null;
  let customerId = null;
  const eventData = event.data.object;

  try {
    switch (event.type) {
      case 'checkout.session.completed':
        console.log('Webhook: Handling checkout.session.completed');
        // Prefer email from metadata if available, fallback to customer details
        customerEmail = eventData.metadata?.email || eventData.customer_details?.email;
        if (customerEmail) {
          await updateStatusByEmail(customerEmail, STATUS_SUBSCRIBED);
        } else {
          console.error('Webhook: checkout.session.completed - Could not determine email from session metadata or customer details.');
        }
        break;

      case 'customer.subscription.created':
        console.log('Webhook: Handling customer.subscription.created');
        // Need to retrieve customer email using customer ID
        customerId = eventData.customer;
        if (customerId) {
          try {
            const customer = await stripe.customers.retrieve(customerId);
            customerEmail = customer.email;
            if (customerEmail) {
              await updateStatusByEmail(customerEmail, STATUS_SUBSCRIBED);
            } else {
               console.error(`Webhook: customer.subscription.created - Customer ${customerId} has no email.`);
            }
          } catch (stripeError) {
             console.error(`Webhook: customer.subscription.created - Error retrieving customer ${customerId}:`, stripeError);
          }
        } else {
           console.error('Webhook: customer.subscription.created - Missing customer ID in event data.');
        }
        break;

      case 'customer.subscription.updated':
        console.log('Webhook: Handling customer.subscription.updated');
        const subscriptionStatus = eventData.status;
        console.log(`Webhook: Subscription status is now ${subscriptionStatus}`);
        // Need customer email
        customerId = eventData.customer;
        if (customerId) {
           try {
             const customer = await stripe.customers.retrieve(customerId);
             customerEmail = customer.email;
             if (customerEmail) {
                // Check if the subscription is active
                if (subscriptionStatus === 'active' || subscriptionStatus === 'trialing') {
                  await updateStatusByEmail(customerEmail, STATUS_SUBSCRIBED);
                } else {
                  // Assume inactive if not active/trialing (e.g., canceled, unpaid, past_due)
                  console.log(`Webhook: Subscription for ${customerEmail} is inactive (${subscriptionStatus}). Setting status to TrialExpired.`);
                  await updateStatusByEmail(customerEmail, STATUS_TRIAL_EXPIRED);
                }
             } else {
                console.error(`Webhook: customer.subscription.updated - Customer ${customerId} has no email.`);
             }
           } catch (stripeError) {
              console.error(`Webhook: customer.subscription.updated - Error retrieving customer ${customerId}:`, stripeError);
           }
        } else {
            console.error('Webhook: customer.subscription.updated - Missing customer ID in event data.');
        }
        break;

      case 'customer.subscription.deleted':
      case 'invoice.payment_failed':
        console.log(`Webhook: Handling ${event.type}`);
        // Need customer email
        customerId = eventData.customer; // For subscription object
        if (!customerId && eventData.object === 'invoice') { // For invoice object
            customerId = eventData.customer;
        }

        if (customerId) {
           try {
             const customer = await stripe.customers.retrieve(customerId);
             customerEmail = customer.email;
             if (customerEmail) {
                console.log(`Webhook: Subscription inactive event (${event.type}) for ${customerEmail}. Setting status to TrialExpired.`);
                await updateStatusByEmail(customerEmail, STATUS_TRIAL_EXPIRED);
             } else {
                console.error(`Webhook: ${event.type} - Customer ${customerId} has no email.`);
             }
           } catch (stripeError) {
              console.error(`Webhook: ${event.type} - Error retrieving customer ${customerId}:`, stripeError);
           }
        } else {
            console.error(`Webhook: ${event.type} - Missing customer ID in event data.`);
        }
        break;

      case 'billing.alert.triggered':
        // Log but take no action on status
        console.log('Webhook: Received billing.alert.triggered. No status change needed.');
        break;

      default:
        console.log(`Webhook: Unhandled event type ${event.type}`);
    }
  } catch (error) {
      console.error(`Webhook: Error processing event ${event.type}:`, error);
      // Decide if we should return 500 to signal Stripe to retry (if applicable)
      // For now, just log and return 200 to acknowledge receipt.
  }

  // Return a 200 response to acknowledge receipt of the event
  res.status(200).end();
});

// Utility function for delay
const delay = ms => new Promise(resolve => setTimeout(resolve, ms));

// Analyze content using Grok API with retry mechanism
async function analyzeContent(url) {
  let lastError;

  for (let attempt = 0; attempt <= config.maxRetries; attempt++) {
    try {
      console.log(`Attempt ${attempt + 1} to analyze content for URL: ${url}`);

      // Create a timeout promise
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('API timeout')), config.timeout);
      });

      // Create the API call promise
      const apiPromise = axios.post(config.grokApiUrl, {
        messages: [
          {
            role: 'system',
            content: "You are an AI assistant. Analyze the following URL for inappropriate content. Respond **only** with a JSON object containing two boolean fields: 'is_unsafe' and 'contains_games'. For example: {\"is_unsafe\": true, \"contains_games\": false}. 'is_unsafe' should be true if the content includes violent, bloody, horror, or pornographic content. If not, but it contains games or entertainment contents (such as videos, animations, novels, pictures, etc), 'contains_games' should be true."
          },
          {
            role: 'user',
            content: `Analyze the following URL: ${url}`
          }
        ],
        model: 'grok-3-mini-beta',
        stream: false,
        temperature: 0
      }, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${config.grokApiKey}`
        },
        timeout: config.timeout
      });

      // Race between timeout and API call
      const response = await Promise.race([apiPromise, timeoutPromise]);
      const content = response.data.choices[0].message.content.trim();
      console.log(`Raw AI response (attempt ${attempt + 1}):`, content);

      const jsonMatch = content.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const jsonString = jsonMatch[0];
        const responseData = JSON.parse(jsonString);
        return {
          is_safe: !responseData.is_unsafe,
          contains_games: responseData.contains_games,
        };
      } else {
        throw new Error(`Invalid JSON response: ${content}`);
      }
    } catch (error) {
      lastError = error;
      console.error(`Attempt ${attempt + 1} failed:`, error);

      if (attempt < config.maxRetries) {
        console.log(`Waiting ${config.retryDelay}ms before retry...`);
        await delay(config.retryDelay);
      }
    }
  }

  console.error('All attempts failed. Last error:', lastError);
  throw new Error(`Failed to analyze content after ${config.maxRetries + 1} attempts: ${lastError.message}`);
}

// --- Authorization Middleware (Example - adapt as needed) ---
// This function determines the user's status and attaches it to the request.
// It will be used by endpoints needing authorization like /check.
async function determineUserStatus(req, res, next) {
  try {
    const ip = (req.headers['x-forwarded-for'] || req.connection.remoteAddress || '').split(',')[0].trim();
    req.ip_hash = crypto.createHash('sha256').update(ip).digest('hex'); // Attach hash to request

    let installation = await getInstallation(req.ip_hash);
    let currentStatus = STATUS_TRIAL_EXPIRED; // Default to expired if unknown
    let isSubscribed = false;
    let userEmail = null;

    if (!installation) {
      // First contact, create record pending email
      installation = await addInstallation(req.ip_hash, STATUS_TRIAL_PENDING_EMAIL);
      currentStatus = STATUS_TRIAL_PENDING_EMAIL;
    } else {
      userEmail = installation.email; // Get email if exists

      // --- Prioritize DB Subscription Status ---
      if (installation.status === STATUS_SUBSCRIBED) {
          console.log(`DB status is Subscribed for ${req.ip_hash}. Skipping trial checks.`);
          currentStatus = STATUS_SUBSCRIBED;
          isSubscribed = true;
          // Optional: Verify with Stripe here if you want extra assurance, but trust the DB first.
          // const subCheck = await checkSubscription(userEmail);
          // if (!subCheck.isSubscribed) { /* Log potential inconsistency */ }
      } else {
          // --- Not Subscribed in DB, proceed with Trial/Expiry Logic ---
          const installDate = installation.install_timestamp;
          const now = new Date();
          const trialExpired = (now - installDate) > TRIAL_PERIOD_MS;

          if (!trialExpired) {
              // Still within trial period
              if (installation.email) {
                  currentStatus = STATUS_TRIAL_ACTIVE;
                  // Update status in DB if it was pending
                  if (installation.status === STATUS_TRIAL_PENDING_EMAIL) {
                      await updateInstallationStatus(req.ip_hash, STATUS_TRIAL_ACTIVE);
                  }
              } else {
                  currentStatus = STATUS_TRIAL_PENDING_EMAIL;
              }
          } else {
              // Trial period is over (and DB status wasn't 'Subscribed')
              if (installation.email) {
                  // Double-check Stripe in case DB status is lagging (e.g., webhook failed)
                  const subCheck = await checkSubscription(installation.email);
                  if (subCheck.isSubscribed) {
                      console.log(`Stripe check confirms subscription for ${req.ip_hash} despite DB status being ${installation.status}. Updating DB.`);
                      currentStatus = STATUS_SUBSCRIBED;
                      isSubscribed = true;
                      await updateInstallationStatus(req.ip_hash, STATUS_SUBSCRIBED); // Correct DB status
                  } else {
                      currentStatus = STATUS_TRIAL_EXPIRED;
                      // Ensure DB status reflects expiry if not already set
                      if (installation.status !== STATUS_TRIAL_EXPIRED) {
                          await updateInstallationStatus(req.ip_hash, STATUS_TRIAL_EXPIRED);
                      }
                  }
              } else {
                  // Trial expired, no email provided yet
                  currentStatus = STATUS_TRIAL_EXPIRED;
                  if (installation.status !== STATUS_TRIAL_EXPIRED) {
                      await updateInstallationStatus(req.ip_hash, STATUS_TRIAL_EXPIRED);
                  }
              }
          }
      }
    }

     // Calculate remaining trial milliseconds if applicable
     let remainingMs = 0;
     if (currentStatus === STATUS_TRIAL_ACTIVE && installation?.install_timestamp) { // Check if install_timestamp exists
         const installDate = installation.install_timestamp;
         const now = new Date();
         const trialEndDate = new Date(installDate.getTime() + TRIAL_PERIOD_MS);
         remainingMs = Math.max(0, trialEndDate.getTime() - now.getTime()); // Ensure non-negative
     }

     // Attach status info to the request object for downstream handlers
     req.userStatus = {
       status: currentStatus,
       subscribed: isSubscribed,
       email: userEmail,
       ip_hash: req.ip_hash,
       remainingMs: currentStatus === STATUS_TRIAL_ACTIVE ? remainingMs : undefined // Only include if trial active
     };
     console.log(`Status for ${req.ip_hash}:`, req.userStatus);
    next();

  } catch (err) {
    console.error('Error in determineUserStatus middleware:', err);
    // Don't block, but maybe return a default "denied" status? Or let endpoint handle?
    // For now, let the endpoint decide based on missing req.userStatus
     req.userStatus = { status: 'Error', subscribed: false, email: null, ip_hash: req.ip_hash };
     next(); // Allow endpoint to handle error status
  }
}


// Enhanced /check endpoint with authorization
app.post('/check', determineUserStatus, async (req, res) => { // Added determineUserStatus middleware
  try {
    console.log('req.body:', req.body);
    const url = req.body.url;
    const { status } = req.userStatus || { status: 'Error' }; // Get status from middleware

    console.log(`Received /check request for URL: ${url} from ${req.ip_hash} with status: ${status}`);

    // --- Authorization Check ---
    if (status !== STATUS_TRIAL_ACTIVE && status !== STATUS_SUBSCRIBED) {
      console.log(`Unauthorized /check attempt by ${req.ip_hash} (status: ${status})`);
      return res.status(403).json({ error: 'Access denied. Please activate your trial or subscribe.' });
    }
    // --- End Authorization Check ---

    if (!url) {
      return res.status(400).json({ error: 'URL is required.' });
    }

    try {
      const analysis = await analyzeContent(url);
      console.log('Analysis result:', analysis);
      res.json(analysis);
    } catch (error) {
      console.error('Analysis error:', error);
      // Send a more specific error response
      if (error.message.includes('timeout')) {
        res.status(504).json({ error: 'Analysis timed out. Please try again.' });
      } else {
        res.status(500).json({
          error: 'Analysis failed',
          details: error.message
        });
      }
    }
  } catch (error) {
    console.error('Error in /check:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Initialize nodemailer transporter globally
const transporter = nodemailer.createTransport({
  host: process.env.SMTP_HOST || 'smtp.gmail.com',
  port: process.env.SMTP_PORT || 465,
  secure: process.env.SMTP_SECURE === 'true',
  auth: {
    user: process.env.FROM_EMAIL || '<EMAIL>',
    pass: process.env.EMAIL_PASSWORD,
  }
});

// Verify SMTP configuration on startup
transporter.verify()
  .then(() => console.log('SMTP configuration is correct'))
  .catch(err => console.error('SMTP configuration error:', err));

app.post('/send-password-email', async (req, res) => {
  try {
    const { email, password } = req.body;
    if (!email || !password) {
      return res.status(400).json({ error: 'Email and password are required.' });
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return res.status(400).json({ error: 'Invalid email format.' });
    }

    await transporter.sendMail({
      from: process.env.FROM_EMAIL || '<EMAIL>',
      to: email,
      subject: 'Your Password for SmartParent',
      text: `Hello,

Your new password for SmartParent is: ${password}

Best regards,
SmartParent Support Team
`,
    });

    console.log(`Password email sent to ${email}`);
    res.json({ success: true });
  } catch (err) {
    console.error('Error sending password email:', err);
    res.status(500).json({ error: 'Failed to send email.' });
  }
});

// --- New Endpoints ---

// Simplified /log-install (now just ensures a record exists)
app.post('/log-install', async (req, res) => {
  try {
    const ip = (req.headers['x-forwarded-for'] || req.connection.remoteAddress || '').split(',')[0].trim();
    const hashedIP = crypto.createHash('sha256').update(ip).digest('hex');

    let installation = await getInstallation(hashedIP);

    if (!installation) {
      // Add with pending status, /status will handle the logic
      await addInstallation(hashedIP, STATUS_TRIAL_PENDING_EMAIL);
      console.log('Installation logged (new) for IP hash:', hashedIP);
    } else {
      console.log('Installation log request for existing IP hash:', hashedIP);
      // Optionally update 'updated_at' timestamp implicitly via trigger if needed
    }
    res.status(200).json({ success: true });
  } catch (err) {
    console.error('Error logging installation:', err);
    res.status(500).json({ error: 'Internal server error' });
  }
});


// Renamed /check-trial to /status and using determineUserStatus middleware
app.get('/status', determineUserStatus, async (req, res) => {
  try {
    // The determineUserStatus middleware already calculated the status
    // and attached it to req.userStatus
    if (!req.userStatus) {
         // Should not happen if middleware is working, but handle defensively
         console.error('User status not found in /status endpoint for IP hash:', req.ip_hash);
         return res.status(500).json({ error: 'Failed to determine user status.' });
    }

    console.log('Responding to /status for', req.ip_hash, 'with:', req.userStatus);
    res.json(req.userStatus);

  } catch (err) {
    // This catch block might be redundant if middleware handles errors,
    // but keep for safety.
    console.error('Error in /status endpoint:', err);
    res.status(500).json({ error: 'Internal server error checking status' });
  }
});


// New /activate endpoint
app.post('/activate', determineUserStatus, async (req, res) => {
  try {
    const { email } = req.body;
    const { status, ip_hash } = req.userStatus; // Get status determined by middleware

    if (!email) {
      return res.status(400).json({ error: 'Email is required.' });
    }
     // Basic email format validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return res.status(400).json({ error: 'Invalid email format.' });
    }

    console.log(`Activation attempt for ${ip_hash} with email ${email}, current status: ${status}`);

    if (status === STATUS_TRIAL_PENDING_EMAIL || status === STATUS_TRIAL_ACTIVE) {
      // User is within the trial period (or was pending email)
      // Update email and set status to TrialActive
      await updateInstallationEmail(ip_hash, email);
      await updateInstallationStatus(ip_hash, STATUS_TRIAL_ACTIVE);
      console.log(`Activated trial for ${ip_hash} with email ${email}`);
      res.json({ status: STATUS_TRIAL_ACTIVE, subscribed: false, email: email });

    } else if (status === STATUS_SUBSCRIBED) {
        // Already subscribed (middleware confirmed via Stripe using existing email)
        // Maybe update email if different? Or just confirm status.
        // For now, just confirm they are subscribed.
        // If the provided email is different from the stored one, maybe update it?
        const installation = await getInstallation(ip_hash);
        if (installation && installation.email !== email) {
            console.log(`Updating email for already subscribed user ${ip_hash} from ${installation.email} to ${email}`);
            await updateInstallationEmail(ip_hash, email);
        }
        res.json({ status: STATUS_SUBSCRIBED, subscribed: true, email: email });

    } else if (status === STATUS_TRIAL_EXPIRED) {
      // Trial expired, check Stripe with the *provided* email now
      const subCheck = await checkSubscription(email);
      if (subCheck.isSubscribed) {
        // They are subscribed with this email, update DB and return subscribed status
        await updateInstallationEmail(ip_hash, email); // Update email if different
        await updateInstallationStatus(ip_hash, STATUS_SUBSCRIBED);
        console.log(`Activated subscription for ${ip_hash} via email ${email} after trial expired.`);
        res.json({ status: STATUS_SUBSCRIBED, subscribed: true, email: email });
      } else {
        // Trial expired and not subscribed with this email
        console.log(`Activation failed for ${ip_hash}, trial expired and email ${email} not subscribed.`);
        // Update the email in the DB anyway, so we know what they tried
        await updateInstallationEmail(ip_hash, email);
        res.json({ status: STATUS_TRIAL_EXPIRED, subscribed: false, email: email });
      }
    } else {
         // Handle unexpected status from middleware (e.g., 'Error')
         console.error(`Unexpected status '${status}' during activation for ${ip_hash}`);
         res.status(500).json({ error: 'Internal server error during activation.' });
    }

  } catch (err) {
    console.error('Error checking trial status:', err);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Add new endpoint for sending history email
app.post('/send-history-email', async (req, res) => {
    try {
        const { email, history } = req.body;
        console.log('Received history email request for:', email);

        if (!email || !history) {
            console.error('Missing email or history data');
            return res.status(400).json({ error: 'Email and history are required.' });
        }

        const historyHTML = history.map(item => `
            <tr>
                <td style="padding: 8px; border: 1px solid #ddd;">${item.title || 'Untitled'}</td>
                <td style="padding: 8px; border: 1px solid #ddd;"><a href="${item.url}">${item.url}</a></td>
                <td style="padding: 8px; border: 1px solid #ddd;">${item.visitCount}</td>
                <td style="padding: 8px; border: 1px solid #ddd;">${formatTime(item.timeSpent)}</td>
            </tr>
        `).join('');

        const emailHTML = `
            <h2>Your Browsing History for ${new Date().toLocaleDateString()}</h2>
            <table style="width: 100%; border-collapse: collapse; margin-top: 20px;">
                <tr style="background-color: #f8f9fa;">
                    <th style="padding: 12px; border: 1px solid #ddd;">Title</th>
                    <th style="padding: 12px; border: 1px solid #ddd;">URL</th>
                    <th style="padding: 12px; border: 1px solid #ddd;">Visits</th>
                    <th style="padding: 12px; border: 1px solid #ddd;">Time Spent</th>
                </tr>
                ${historyHTML}
            </table>
        `;

        await transporter.sendMail({
            from: process.env.FROM_EMAIL || '<EMAIL>',
            to: email,
            subject: "Today's browsing history",
            html: emailHTML
        });

        console.log('History email sent successfully to:', email);
        res.json({ success: true });
    } catch (err) {
        console.error('Error sending history email:', err);
        res.status(500).json({ error: 'Failed to send history email.' });
    }
});

// Helper function to format seconds into readable time
function formatTime(seconds) {
    if (seconds < 60) return `${seconds}s`;
    const minutes = Math.floor(seconds / 60);
    if (minutes < 60) return `${minutes}m ${seconds % 60}s`;
    const hours = Math.floor(minutes / 60);
    return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
}

// --- TipTop Endpoint Helper Functions ---

// Helper function to validate URL format (basic check)
function isValidHttpUrl(string) {
  let url;
  try {
    url = new URL(string);
  } catch (_) {
    return false;
  }
  return url.protocol === "http:" || url.protocol === "https:";
}

async function fetchAndParse(url) {
  try {
    const response = await axios.get(url, { timeout: config.timeout });
    const dom = new JSDOM(response.data, { url });
    const reader = new Readability(dom.window.document);
    const article = reader.parse();
    return article; // Contains title, content (HTML), textContent, length, excerpt, siteName
  } catch (error) {
    console.error(`Error fetching or parsing URL ${url}:`, error);
    throw new Error(`Failed to fetch or parse URL: ${error.message}`);
  }
}

// Summarize text using Grok API with retry and timeout
async function summarizeText(text) {
  if (!text || text.trim().length === 0) {
    console.log('Skipping summarization for empty text.');
    return "No content available for summarization.";
  }

  console.log(`Attempting to summarize text (length: ${text.length})`);
  let lastError;

  // Limit text length to avoid exceeding API limits (adjust limit as needed)
  const MAX_TEXT_LENGTH = 15000; // Example limit, adjust based on Grok API constraints
  const truncatedText = text.length > MAX_TEXT_LENGTH ? text.substring(0, MAX_TEXT_LENGTH) + "..." : text;


  for (let attempt = 0; attempt <= config.maxRetries; attempt++) {
    try {
      console.log(`Summarization attempt ${attempt + 1}`);

      // Create a timeout promise
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Grok API timeout during summarization')), config.timeout);
      });

      // Create the API call promise
      const apiPromise = axios.post(config.grokApiUrl, {
        messages: [
          {
            role: 'system',
            content: "You are an AI assistant. Summarize the following text concisely in 2-3 sentences."
          },
          {
            role: 'user',
            content: truncatedText // Send potentially truncated text
          }
        ],
        model: 'grok-3-mini-fast-beta', // Or choose another suitable model if available
        stream: false,
        temperature: 0.5 // Adjust temperature for summarization creativity/factuality
      }, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${config.grokApiKey}`
        },
        timeout: config.timeout // Axios timeout
      });

      // Race between timeout and API call
      const response = await Promise.race([apiPromise, timeoutPromise]);

      if (response.data && response.data.choices && response.data.choices.length > 0) {
        const summary = response.data.choices[0].message.content.trim();
        console.log(`Summarization successful (attempt ${attempt + 1})`);
        return summary;
      } else {
        throw new Error('Invalid response structure from Grok API');
      }
    } catch (error) {
      lastError = error;
      console.error(`Summarization attempt ${attempt + 1} failed:`, error.message);

      if (attempt < config.maxRetries) {
        console.log(`Waiting ${config.retryDelay}ms before retry...`);
        await delay(config.retryDelay);
      }
    }
  }

  console.error('All summarization attempts failed. Last error:', lastError?.message);
  // Return a user-friendly error message
  return "Could not generate summary at this time.";
}

// Extract keywords using Grok API
async function extractKeywords(text) {
  if (!text || text.trim().length === 0) {
    console.log('Skipping keyword extraction for empty text.');
    return [];
  }

  console.log(`Attempting to extract keywords (text length: ${text.length})`);
  let lastError;

  // Limit text length (adjust as needed)
  const MAX_TEXT_LENGTH = 15000;
  const truncatedText = text.length > MAX_TEXT_LENGTH ? text.substring(0, MAX_TEXT_LENGTH) + "..." : text;

  for (let attempt = 0; attempt <= config.maxRetries; attempt++) {
    try {
      console.log(`Keyword extraction attempt ${attempt + 1}`);

      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Grok API timeout during keyword extraction')), config.timeout);
      });

      const apiPromise = axios.post(config.grokApiUrl, {
        messages: [
          {
            role: 'system',
            content: "You are an AI assistant. Extract the 5 most relevant keywords or key phrases from the following text. Respond **only** with a JSON array of strings, like [\"keyword1\", \"key phrase 2\", \"keyword3\"]."
          },
          {
            role: 'user',
            content: truncatedText
          }
        ],
        model: 'grok-2-latest',
        stream: false,
        temperature: 0.2 // Lower temperature for more deterministic keyword extraction
      }, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${config.grokApiKey}`
        },
        timeout: config.timeout
      });

      const response = await Promise.race([apiPromise, timeoutPromise]);

      if (response.data && response.data.choices && response.data.choices.length > 0) {
        const content = response.data.choices[0].message.content.trim();
        console.log(`Raw keyword response (attempt ${attempt + 1}):`, content);
        // Try to parse the JSON array directly
        try {
          const keywords = JSON.parse(content);
          if (Array.isArray(keywords) && keywords.every(k => typeof k === 'string')) {
            console.log(`Keyword extraction successful (attempt ${attempt + 1})`);
            return keywords.slice(0, 5); // Ensure max 5 keywords
          } else {
             throw new Error('Parsed response is not a string array.');
          }
        } catch (parseError) {
          console.error(`Failed to parse keywords JSON (attempt ${attempt + 1}):`, parseError);
          throw new Error(`Invalid JSON response for keywords: ${content}`);
        }
      } else {
        throw new Error('Invalid response structure from Grok API for keywords');
      }
    } catch (error) {
      lastError = error;
      console.error(`Keyword extraction attempt ${attempt + 1} failed:`, error.message);

      if (attempt < config.maxRetries) {
        console.log(`Waiting ${config.retryDelay}ms before retry...`);
        await delay(config.retryDelay);
      }
    }
  }

  console.error('All keyword extraction attempts failed. Last error:', lastError?.message);
  // Return empty array on failure
  return [];
}

// Find tips and links using Grok API
async function findTipsAndLinks(keywords, userContext) {
  // TODO: Potentially use userContext to personalize tips/links
  console.log('Finding tips/links for keywords:', keywords);

  if (!keywords || keywords.length === 0) {
    console.log('Skipping tips/links generation due to no keywords.');
    return { tips: [], links: [] };
  }

  let lastError;
  const keywordsString = keywords.join(', '); // Format keywords for the prompt

  for (let attempt = 0; attempt <= config.maxRetries; attempt++) {
    try {
      console.log(`Tips/links generation attempt ${attempt + 1}`);

      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Grok API timeout during tips/links generation')), config.timeout);
      });

      // Refined prompt for better structure and validation guidance
      const apiPrompt = `Given the keywords: "${keywordsString}", generate relevant tips and links.
Provide exactly 2 tips and 2 links.
Respond **only** with a valid JSON object following this exact structure:
{
  "tips": [
    {"title": "string", "description": "string", "url": "valid_url_or_null (optional)"}
  ],
  "links": [
    {"title": "string", "url": "valid_url (required)"}
  ]
}
Ensure all provided URLs start with http:// or https://. If no suitable URL exists for a tip, set its url value to null.`;

      const apiPromise = axios.post(config.grokApiUrl, {
        messages: [
          {
            role: 'system',
            content: "You are an AI assistant that provides helpful tips and links related to given keywords."
          },
          {
            role: 'user',
            content: apiPrompt
          }
        ],
        model: 'grok-2-latest',
        stream: false,
        temperature: 0.6 // Slightly higher temperature for more creative tips/links
      }, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${config.grokApiKey}`
        },
        timeout: config.timeout
      });

      const response = await Promise.race([apiPromise, timeoutPromise]);

      if (response.data && response.data.choices && response.data.choices.length > 0) {
        const content = response.data.choices[0].message.content.trim();
        console.log(`Raw tips/links response (attempt ${attempt + 1}):`, content);
        // Try to parse the JSON object directly
        try {
          // Find the JSON part in case the model adds extra text
          const jsonMatch = content.match(/\{[\s\S]*\}/);
          if (!jsonMatch) {
              throw new Error("No JSON object found in the response.");
          }
          const jsonString = jsonMatch[0];
          const result = JSON.parse(jsonString);

          // Validate the structure and URLs
          if (result && Array.isArray(result.tips) && Array.isArray(result.links)) {
            const validatedTips = result.tips.filter(tip =>
                tip && typeof tip.title === 'string' && typeof tip.description === 'string' && (tip.url === null || (typeof tip.url === 'string' && isValidHttpUrl(tip.url)))
            ).slice(0, 2); // Limit to 2 valid tips

            const validatedLinks = result.links.filter(link =>
                link && typeof link.title === 'string' && typeof link.url === 'string' && isValidHttpUrl(link.url)
            ).slice(0, 2); // Limit to 2 valid links

            console.log(`Tips/links generation successful (attempt ${attempt + 1}), Validated Tips: ${validatedTips.length}, Validated Links: ${validatedLinks.length}`);
            return {
                tips: validatedTips,
                links: validatedLinks
            };
          } else {
            throw new Error('Parsed response JSON does not have the expected tips/links array structure.');
          }
        } catch (parseError) {
          console.error(`Failed to parse tips/links JSON (attempt ${attempt + 1}):`, parseError, 'Raw content:', content);
          throw new Error(`Invalid JSON response for tips/links: ${content}`);
        }
      } else {
        throw new Error('Invalid response structure from Grok API for tips/links');
      }
    } catch (error) {
      lastError = error;
      console.error(`Tips/links generation attempt ${attempt + 1} failed:`, error.message);

      if (attempt < config.maxRetries) {
        console.log(`Waiting ${config.retryDelay}ms before retry...`);
        await delay(config.retryDelay);
      }
    }
  }

  console.error('All tips/links generation attempts failed. Last error:', lastError?.message);
  // Return empty arrays on failure
  return { tips: [], links: [] };
}

// The main TipTop endpoint handler
async function handleTipTopRequest(req, res) {
  const startTime = Date.now();
  try {
    const { url, userContext } = req.body;
    console.log(`Received /tiptop request for URL: ${url}`);

    if (!url) {
      return res.status(400).json({ error: 'URL is required.' });
    }

    // 1. Fetch and Parse
    const article = await fetchAndParse(url);
    if (!article || !article.textContent) {
      return res.status(400).json({ error: 'Could not extract readable content from the URL.' });
    }

    // 2. Summarize
    const summaryText = await summarizeText(article.textContent);

    // 3. Extract Keywords
    const keywords = await extractKeywords(article.textContent);

    // 4. Find Tips and Links
    const { tips, links } = await findTipsAndLinks(keywords, userContext);

    // 5. Construct Response
    const responseTime = Date.now() - startTime;
    const responsePayload = {
      summary: {
        text: summaryText,
        source: "Grok AI" // Updated source
      },
      tips: tips,
      links: links,
      keywords: keywords,
      responseTime: responseTime
    };

    console.log(`Sending /tiptop response for URL: ${url}`);
    res.json(responsePayload);

  } catch (error) {
    console.error('Error in /tiptop endpoint:', error);
    res.status(500).json({
        error: 'Internal server error processing TipTop request',
        details: error.message
     });
  }
}

// Register the TipTop endpoint
app.post('/tiptop', handleTipTopRequest);

// Health check endpoint for Kubernetes probes
app.get('/check', (req, res) => {
  // Silent health check - status is logged by morgan middleware
  res.status(200).json({
    status: 'OK',
    timestamp: new Date().toISOString()
  });
});

// ACME Challenge endpoint for Let's Encrypt validation
app.get('/.well-known/acme-challenge/:token', (req, res) => {
  res.status(200).send('OK');
});

// Fallback route for undefined paths
app.use((req, res) => {
  // Don't log 404s for known security probe paths
  const isSuspiciousPath = req.path.toLowerCase().includes('/.env') ||
                          req.path.toLowerCase().includes('/docker') ||
                          req.path.toLowerCase().includes('/.git') ||
                          req.path.toLowerCase().includes('/wp-');

  if (!isSuspiciousPath) {
    console.log(`Received ${req.method} request at ${req.originalUrl} - 404`);
  }
  res.status(404).send('Page not found');
});

// Export the Express app as the Cloud Function
functions.http('AIWebMon', app);

