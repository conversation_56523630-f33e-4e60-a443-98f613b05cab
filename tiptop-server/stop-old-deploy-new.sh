#!/bin/bash

# TipTop Clean Deployment Script
# Stop old TipTop in smartparent namespace, deploy new TipTop in tiptop namespace

# Function to handle errors
handle_error() {
    echo "❌ Error occurred in deployment script at line $1"
    exit 1
}

trap 'handle_error $LINENO' ERR

# Function to show usage
show_usage() {
    echo "Usage: $0 <GCP_PROJECT_ID>"
    echo ""
    echo "This script will:"
    echo "1. Stop old TipTop deployment in smartparent namespace"
    echo "2. Deploy new TipTop with WebSocket server in tiptop namespace"
    echo "3. Use the same domains (tiptop.qubitrhythm.com, ws.tiptop.qubitrhythm.com)"
    echo ""
    echo "Arguments:"
    echo "  GCP_PROJECT_ID  - Your GCP project ID"
    echo ""
    echo "Example:"
    echo "  $0 my-gcp-project-123"
    exit 1
}

# Check arguments
if [ -z "$1" ]; then
    echo "❌ Error: GCP Project ID is required"
    show_usage
fi

GCP_PROJECT_ID=$1

echo "🔄 TipTop Clean Deployment"
echo "=========================="
echo "📋 Project ID: $GCP_PROJECT_ID"
echo ""

# Check if old TipTop exists
echo "🔍 Checking for old TipTop deployment..."
if ! kubectl get deployment cloud-function-tiptop-deployment -n smartparent >/dev/null 2>&1; then
    echo "✅ No old TipTop deployment found in smartparent namespace"
    echo "💡 You can proceed directly with fresh deployment:"
    echo "   ./deploy-to-gcp.sh $GCP_PROJECT_ID"
    exit 0
fi

echo "✅ Found old TipTop deployment in smartparent namespace"
echo ""

# Show current old TipTop resources
echo "📊 Current old TipTop resources:"
kubectl get all -n smartparent | grep tiptop
echo ""

# Show current ingress (to confirm domains)
echo "🌐 Current TipTop domains:"
if kubectl get ingress -n smartparent >/dev/null 2>&1; then
    kubectl get ingress -n smartparent -o custom-columns="NAME:.metadata.name,HOSTS:.spec.rules[*].host" | grep -i tiptop || echo "   No TipTop ingress found"
else
    echo "   No ingress found in smartparent namespace"
fi
echo ""

# Confirm with user
echo "⚠️  This will:"
echo "   1. 🛑 Stop old TipTop deployment (brief downtime)"
echo "   2. 🗑️  Remove old TipTop resources from smartparent namespace"
echo "   3. 🚀 Deploy new TipTop with WebSocket server to tiptop namespace"
echo "   4. 🌐 Use same domains (tiptop.qubitrhythm.com, ws.tiptop.qubitrhythm.com)"
echo ""
echo "💡 Estimated downtime: 2-5 minutes"
echo ""

read -p "Do you want to proceed? (y/N): " confirm
if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
    echo "Deployment cancelled."
    exit 0
fi

echo ""
echo "🛑 Step 1: Stopping old TipTop deployment..."
echo "--------------------------------------------"

# Stop old TipTop deployment
echo "Scaling down old TipTop deployment..."
kubectl scale deployment cloud-function-tiptop-deployment --replicas=0 -n smartparent

# Wait a moment for pods to terminate
echo "Waiting for pods to terminate..."
sleep 10

# Delete old TipTop resources
echo "Removing old TipTop resources..."
kubectl delete deployment cloud-function-tiptop-deployment -n smartparent
kubectl delete service cloud-function-tiptop-service -n smartparent

# Delete old TipTop ingress if it exists
if kubectl get ingress -n smartparent -o name | grep -i tiptop >/dev/null 2>&1; then
    echo "Removing old TipTop ingress..."
    kubectl get ingress -n smartparent -o name | grep -i tiptop | xargs kubectl delete -n smartparent
fi

# Delete old TipTop secrets if they exist
if kubectl get secrets -n smartparent -o name | grep -i tiptop >/dev/null 2>&1; then
    echo "Removing old TipTop secrets..."
    kubectl get secrets -n smartparent -o name | grep -i tiptop | xargs kubectl delete -n smartparent
fi

echo "✅ Old TipTop deployment stopped and cleaned up"
echo ""

echo "🚀 Step 2: Deploying new TipTop..."
echo "----------------------------------"

# Deploy new TipTop
echo "Starting fresh TipTop deployment..."
./deploy-to-gcp.sh $GCP_PROJECT_ID

echo ""
echo "🎯 Step 3: Configuring production mode..."
echo "----------------------------------------"

# Switch to production mode
echo "Switching to production mode..."
../tiptop-toggle-mode.sh production

echo ""
echo "✅ Deployment completed successfully!"
echo ""
echo "📊 New TipTop Status:"
kubectl get pods -n tiptop
echo ""
echo "🌐 New TipTop Services:"
kubectl get services -n tiptop
echo ""
echo "🔗 New TipTop Ingress:"
kubectl get ingress -n tiptop
echo ""
echo "🎯 Next Steps:"
echo "1. Wait for SSL certificates to be issued (5-10 minutes)"
echo "2. Test the endpoints:"
echo "   - API: https://tiptop.qubitrhythm.com/tiptop"
echo "   - WebSocket: wss://ws.tiptop.qubitrhythm.com"
echo "3. Test the extension with a webpage"
echo ""
echo "🔍 Monitoring:"
echo "  - API logs: kubectl logs deployment/cloud-function-tiptop-deployment -n tiptop -f"
echo "  - WebSocket logs: kubectl logs deployment/tiptop-websocket -n tiptop -f"
echo ""
echo "🎉 TipTop is now running with WebSocket support in a clean tiptop namespace!"
