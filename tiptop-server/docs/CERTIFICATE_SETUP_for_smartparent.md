# SmartParent and Extension TLS Certificate Management Guide

## 1. DNS Configuration (GoDaddy)
```bash
# 1.1 NS Record Delegation to GCP
- Remove existing A/CNAME records in GoDaddy for smartparent.qubitrhythm.com
- Create NS records for smartparent.qubitrhythm.com pointing using GCP Cloud DNS nameservers:
  ns-cloud-a1.googledomains.com
  ns-cloud-a2.googledomains.com
  ns-cloud-a3.googledomains.com
  ns-cloud-a4.googledomains.com

# 1.2 Add A record for extension
- Add A record for extension.smartparent.qubitrhythm.com pointing to 34.135.81.104 with TTL 600 seconds

# 1.3 Add CNAME record for ACME challenge
- Add CNAME record for _acme-challenge.extension.smartparent.qubitrhythm.com pointing to acme-challenge.smartparent.qubitrhythm.com. with TTL 1 Hour

# 1.4 Verify DNS Propagation
dig +trace NS smartparent.qubitrhythm.com
nslookup -type=SOA smartparent.qubitrhythm.com *******
```

## 2. GCP Infrastructure Setup
```bash
# 2.1 Enable Required APIs
gcloud services enable \
  dns.googleapis.com \
  container.googleapis.com \
  compute.googleapis.com

# 2.2 Create Cloud DNS Zone
gcloud dns managed-zones create smartparent-dns \
  --description="Production DNS zone" \
  --dns-name="smartparent.qubitrhythm.com" \
  --visibility=public

# 2.3 Configure IAM Permissions
gcloud projects add-iam-policy-binding smartparent \
  --member="serviceAccount:<EMAIL>" \
  --role="roles/dns.admin"

# 2.4 Network Requirements
- Open port 80/tcp for ACME HTTP-01 challenges
- Open port 443/tcp for HTTPS traffic
- Configure firewall rules to allow GCE L7 LB communication
```

## 3. Cert-Manager Installation
```bash
# 3.1 Add Helm Repository
helm repo add jetstack https://charts.jetstack.io
helm repo update

# 3.2 Install Cert-Manager
helm upgrade cert-manager jetstack/cert-manager \
  --install \
  --namespace cert-manager \
  --create-namespace \
  --version v1.13.2 \
  --set installCRDs=true \
  --set prometheus.enabled=false
```

## 4. Certificate Issuer Configuration
```yaml
# k8s/cert-issuer.yaml
apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: letsencrypt-prod
spec:
  acme:
    email: <EMAIL>
    server: https://acme-v02.api.letsencrypt.org/directory
    privateKeySecretRef:
      name: letsencrypt-prod-key
    solvers:
    - http01:
        ingress:
          class: gce
          serviceType: ClusterIP
```

## 5. Ingress Controller Configuration for smartparent.qubitrhythm.com
```yaml
# k8s/cloud-function-ingress.yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: smartparent-ingress
  annotations:
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    kubernetes.io/ingress.class: "gce"
    networking.gke.io/managed-certificates: "smartparent-cert"
spec:
  tls:
  - hosts: [smartparent.qubitrhythm.com]
    secretName: smartparent-tls
  rules:
  - host: smartparent.qubitrhythm.com
    http:
      paths:
      - path: /*
        backend:
          service:
            name: cloud-function-service
            port:
              number: 80
```

## 5.1. Ingress Controller Configuration for extension.smartparent.qubitrhythm.com
To enable TLS for `extension.smartparent.qubitrhythm.com`, you need to add it to the `hosts` section in the `tls` and `rules` sections of the Ingress configuration. The existing configuration should already include this, but verify the `k8s/cloud-function-ingress.yaml` to ensure it includes `extension.smartparent.qubitrhythm.com` in the `hosts` list under `tls` and has a rule defined for `extension.smartparent.qubitrhythm.com`.

```yaml
# k8s/cloud-function-ingress.yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: cloud-function-ingress
  namespace: smartparent
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt-prod
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    cert-manager.io/acme-challenge-type: http01
    nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/cors-allow-methods: "GET, PUT, POST, DELETE, OPTIONS"
    nginx.ingress.kubernetes.io/cors-allow-headers: "DNT,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization"
    nginx.ingress.kubernetes.io/cors-allow-origin: "chrome-extension://*, https://smartparent.qubitrhythm.com"
    nginx.ingress.kubernetes.io/cors-allow-credentials: "true"
spec:
  ingressClassName: nginx
  tls:
  - hosts:
    - smartparent.qubitrhythm.com
    - extension.smartparent.qubitrhythm.com # Ensure this line is present
    secretName: smartparent-tls
  rules:
  - host: smartparent.qubitrhythm.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: cloud-function
            port:
              number: 80
  - host: extension.smartparent.qubitrhythm.com # Ensure this rule is present
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: cloud-function
            port:
              number: 80
```

**Verification Steps:**

1.  **Check Ingress YAML**: Ensure `extension.smartparent.qubitrhythm.com` is listed under `tls.hosts` and within `rules`.
2.  **Apply Ingress**: If modifications were made, apply the updated Ingress configuration:
    ```bash
    kubectl apply -f k8s/cloud-function-ingress.yaml
    ```
3.  **DNS Records**: Verify that DNS records for `extension.smartparent.qubitrhythm.com` are correctly pointing to your Ingress Load Balancer IP. This might involve creating a new A record or CNAME record in your DNS provider (GoDaddy).
4.  **Certificate Status**: Monitor the certificate status to ensure a certificate is issued for `extension.smartparent.qubitrhythm.com`.
    ```bash
    kubectl get certificate smartparent-tls -n smartparent -o wide
    ```
    or
    ```bash
    watch -n 2 "kubectl get certificaterequest,order,challenge -A"
    ```
5.  **Access Extension Domain**: Once the certificate is issued and DNS is propagated, try accessing `https://extension.smartparent.qubitrhythm.com` to confirm TLS is working correctly.

This expanded section provides specific steps for including `extension.smartparent.qubitrhythm.com` in the certificate setup, focusing on verifying the Ingress configuration and DNS settings.
# SmartParent and Extension TLS Certificate Management Guide

## 1. DNS Configuration (GoDaddy)
```bash
# 1.1 NS Record Delegation to GCP
- Remove existing A/CNAME records in GoDaddy
- Create NS records for the A record smartparent.qubitrhythm.compointing using GCP Cloud DNS nameservers:
  ns-cloud-a1.googledomains.com
  ns-cloud-a2.googledomains.com
  ns-cloud-a3.googledomains.com
  ns-cloud-a4.googledomains.com

# 1.2 Verify DNS Propagation
dig +trace NS smartparent.qubitrhythm.com
nslookup -type=SOA smartparent.qubitrhythm.com *******
```

## 2. GCP Infrastructure Setup
```bash
# 2.1 Enable Required APIs
gcloud services enable \
  dns.googleapis.com \
  container.googleapis.com \
  compute.googleapis.com

# 2.2 Create Cloud DNS Zone
gcloud dns managed-zones create smartparent-dns \
  --description="Production DNS zone" \
  --dns-name="smartparent.qubitrhythm.com" \
  --visibility=public

# 2.3 Configure IAM Permissions
gcloud projects add-iam-policy-binding smartparent \
  --member="serviceAccount:<EMAIL>" \
  --role="roles/dns.admin"

# 2.4 Network Requirements
- Open port 80/tcp for ACME HTTP-01 challenges
- Open port 443/tcp for HTTPS traffic
- Configure firewall rules to allow GCE L7 LB communication
```

## 3. Cert-Manager Installation
```bash
# 3.1 Add Helm Repository
helm repo add jetstack https://charts.jetstack.io
helm repo update

# 3.2 Install Cert-Manager
helm upgrade cert-manager jetstack/cert-manager \
  --install \
  --namespace cert-manager \
  --create-namespace \
  --version v1.13.2 \
  --set installCRDs=true \
  --set prometheus.enabled=false
```

## 4. Certificate Issuer Configuration
```yaml
# k8s/cert-issuer.yaml
apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: letsencrypt-prod
spec:
  acme:
    email: <EMAIL>
    server: https://acme-v02.api.letsencrypt.org/directory
    privateKeySecretRef:
      name: letsencrypt-prod-key
    solvers:
    - http01:
        ingress:
          class: gce
          serviceType: ClusterIP
```

## 5. Ingress Controller Configuration
```yaml
# k8s/cloud-function-ingress.yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: smartparent-ingress
  annotations:
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    kubernetes.io/ingress.class: "gce"
    networking.gke.io/managed-certificates: "smartparent-cert"
spec:
  tls:
  - hosts: [smartparent.qubitrhythm.com]
    secretName: smartparent-tls
  rules:
  - host: smartparent.qubitrhythm.com
    http:
      paths:
      - path: /*
        backend:
          service:
            name: cloud-function-service
            port: 
              number: 80
```

## 6. Deployment & Validation
```bash
# 6.1 Apply Configurations
kubectl apply -f k8s/cert-issuer.yaml
kubectl apply -f k8s/cloud-function-ingress.yaml

# 6.2 Monitor Certificate Issuance
watch -n 2 "kubectl get certificaterequest,order,challenge -A"

# 6.3 Verify Certificate Status
kubectl get certificate smartparent-tls -n smartparent -o wide
```

## 7. Troubleshooting Guide
```bash
# 7.1 Inspect ACME Challenges
kubectl describe challenges.acme.cert-manager.io -n smartparent

# 7.2 Check Cert-Manager Logs
kubectl logs -n cert-manager deploy/cert-manager --tail=100

# 7.3 Verify Load Balancer IP
gcloud compute addresses describe smartparent-lb-ip --global

# 7.4 Test DNS Records
dig +short smartparent.qubitrhythm.com
nslookup -type=TXT _acme-challenge.smartparent.qubitrhythm.com
```

## 8. Maintenance Procedures
```bash
# 8.1 Certificate Renewal Monitoring
kubectl get certificates -w -n smartparent

# 8.2 Cert-Manager Upgrade
helm repo update
helm upgrade cert-manager jetstack/cert-manager -n cert-manager --version v1.13.2

# 8.3 Annual Security Audit
gcloud dns record-sets list --zone=smartparent-dns \
  --filter="type=NS OR type=SOA"
```

## 9. Architecture Overview
```mermaid
graph TD
    A[GoDaddy DNS] -->|NS Records| B[GCP Cloud DNS]
    B -->|A Record| C[GCE Load Balancer]
    C -->|HTTPS| D[Kubernetes Ingress]
    D -->|Cert Request| E[Cert-Manager]
    E -->|ACME Challenge| C
    C -->|Validation| E
    E -->|Issues Certificate| D
    D -->|TLS Termination| F[Cloud Function Service]
```

## 10. Security Best Practices
```bash
# 10.1 Secret Rotation
kubectl delete secret letsencrypt-prod-key -n cert-manager
kubectl apply -f k8s/cert-issuer.yaml

# 10.2 Certificate Inspection
kubectl get secret smartparent-tls -n smartparent -o jsonpath='{.data.tls\.crt}' | base64 -d | openssl x509 -text -noout

# 10.3 Network Security
gcloud compute firewall-rules list --filter="name~gke-cert-manager"
```
```bash
# 1.1 NS Record Delegation
- Remove existing A/CNAME records
- Create NS records pointing to GCP Cloud DNS:
  ns-cloud-{a1,a2,a3,a4}.googledomains.com
- Propagation verification:
  dig +trace NS smartparent.qubitrhythm.com

# 1.2 Domain Verification
- TXT record for domain ownership (auto-created by GCP)
- Check propagation:
  dig +short TXT smartparent.qubitrhythm.com
```

## 2. GCP Foundation Setup
```bash
# 2.1 API Enablement
gcloud services enable \
  dns.googleapis.com \
  container.googleapis.com \
  compute.googleapis.com

# 2.2 Cloud DNS Zone Creation
gcloud dns managed-zones create smartparent-dns \
  --description="Production DNS zone" \
  --dns-name="smartparent.qubitrhythm.com" \
  --visibility=public \
  --project=smartparent

# 2.3 IAM Permissions
gcloud projects add-iam-policy-binding smartparent \
  --member="serviceAccount:<EMAIL>" \
  --role="roles/dns.admin"
```

## 3. Cert-Manager Installation
```bash
# 3.1 Helm Setup
helm repo add jetstack https://charts.jetstack.io
helm repo update

# 3.2 Cluster Installation
helm upgrade cert-manager jetstack/cert-manager \
  --install \
  --namespace cert-manager \
  --create-namespace \
  --version v1.13.2 \
  --set installCRDs=true \
  --set prometheus.enabled=false
```

## 4. Certificate Authority Configuration
```yaml
# k8s/cert-issuer.yaml (Single Source of Truth)
apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: letsencrypt-prod
spec:
  acme:
    email: <EMAIL>
    server: https://acme-v02.api.letsencrypt.org/directory
    privateKeySecretRef:
      name: letsencrypt-prod-key
    solvers:
    - http01:
        ingress:
          class: gce
          serviceType: ClusterIP
          podTemplate:
            spec:
              nodeSelector:
                cloud.google.com/gke-nodepool: default-pool
```

## 5. Ingress Controller Setup
```yaml
# k8s/cloud-function-ingress.yaml (Definitive Version)
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: smartparent-ingress
  annotations:
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    kubernetes.io/ingress.class: "gce"
    networking.gke.io/managed-certificates: "smartparent-cert"
    kubernetes.io/ingress.allow-http: "false"
spec:
  defaultBackend:
    service:
      name: cloud-function-service
      port:
        number: 80
  tls:
  - hosts: [smartparent.qubitrhythm.com]
    secretName: smartparent-tls
  rules:
  - host: smartparent.qubitrhythm.com
    http:
      paths:
      - path: /*
        backend:
          service:
            name: cloud-function-service
            port: 
              number: 80
```

## 6. Validation & Debugging
```bash
# 6.1 Deployment Commands
kubectl apply -f k8s/cert-issuer.yaml
kubectl apply -f k8s/cloud-function-ingress.yaml

# 6.2 Real-time Monitoring
watch -n 2 "kubectl get certificaterequest,order,challenge -A"

# 6.3 Certificate Inspection
kubectl get certificate smartparent-tls -n smartparent -o wide
kubectl describe certificaterequest -n smartparent

# 6.4 Network Diagnostics
gcloud compute addresses describe smartparent-lb-ip --global
gcloud compute backend-services list --format="table(name,backends[].group.scope())"
```

## 7. Maintenance Protocol
```bash
# 7.1 Renewal Monitoring
kubectl get certificates -w -n smartparent

# 7.2 Cert-Manager Updates
helm repo update
helm upgrade cert-manager jetstack/cert-manager -n cert-manager --version v1.13.2

# 7.3 DNS Health Checks
gcloud dns managed-zones describe smartparent-dns --format="json(dnsName,nameServers,visibility)"
dig +short NS smartparent.qubitrhythm.com @*******
```

## 8. Architectural Overview
```mermaid
graph LR
    A[GoDaddy DNS] -->|NS Delegation| B[GCP Cloud DNS]
    B -->|A Record| C[GCE Load Balancer]
    C -->|HTTPS| D[K8s Ingress]
    D -->|Cert Request| E[Cert-Manager]
    E -->|ACME Challenge| C
    C -->|Validation| E
    E -->|TLS Certificate| D
```

## 9. Security Considerations
```bash
# 9.1 Firewall Rules
- Inbound: 80/tcp (ACME), 443/tcp (HTTPS)
- Managed by GCE ingress controller
- Automatic rule cleanup post-challenge

# 9.2 Secret Management
kubectl get secret smartparent-tls -n smartparent -o jsonpath='{.data.tls\.crt}' | base64 -d
kubectl get secret letsencrypt-prod-key -n cert-manager -o jsonpath='{.data.tls\.key}'
```

## 10. Failure Recovery
```bash
# 10.1 Certificate Renewal
kubectl delete certificate smartparent-tls -n smartparent && kubectl apply -f k8s/cloud-function-ingress.yaml

# 10.2 Full Reissue
kubectl delete secret letsencrypt-prod-key -n cert-manager
kubectl apply -f k8s/cert-issuer.yaml
```
```bash
# Required records:
1. NS records (delegation to GCP Cloud DNS):
   - ns-cloud-a1.googledomains.com
   - ns-cloud-a2.googledomains.com 
   - ns-cloud-a3.googledomains.com
   - ns-cloud-a4.googledomains.com
2. Remove any existing A/CNAME records
3. Propagation check:
   dig +trace NS smartparent.qubitrhythm.com
```

## 2. GCP Prerequisites
```bash
# 2.1 Enable APIs
gcloud services enable \
  dns.googleapis.com \
  container.googleapis.com \
  compute.googleapis.com

# 2.2 Create managed zone
gcloud dns managed-zones create smartparent-dns \
  --description="Primary DNS zone" \
  --dns-name=smartparent.qubitrhythm.com \
  --visibility=public \
  --project=smartparent

# 2.3 IAM permissions
gcloud projects add-iam-policy-binding smartparent \
  --member=serviceAccount:<EMAIL> \
  --role=roles/dns.admin

# 2.4 Verify domain ownership
gcloud domains verify smartparent.qubitrhythm.com \
  --project=smartparent \
  --method=DNS \
  --create-txt-records
```

## 3. Cert-Manager Installation
```bash
# 3.1 Add Helm repo
helm repo add jetstack https://charts.jetstack.io
helm repo update

# 3.2 Install cert-manager
helm upgrade cert-manager jetstack/cert-manager \
  --install \
  --namespace cert-manager \
  --create-namespace \
  --version v1.13.2 \
  --set installCRDs=true \
  --set prometheus.enabled=false
```

## 4. Certificate Issuer Configuration
```yaml
# k8s/cert-issuer.yaml
apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: letsencrypt-prod
spec:
  acme:
    email: <EMAIL>
    server: https://acme-v02.api.letsencrypt.org/directory
    privateKeySecretRef:
      name: letsencrypt-prod-key
    solvers:
    - http01:
        ingress:
          class: gce
          serviceType: ClusterIP
          podTemplate:
            spec:
              nodeSelector:
                cloud.google.com/gke-nodepool: default-pool
```

## 5. Ingress Configuration
```yaml
# k8s/cloud-function-ingress.yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: smartparent-ingress
  annotations:
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    kubernetes.io/ingress.class: "gce"
    networking.gke.io/managed-certificates: "smartparent-cert"
    kubernetes.io/ingress.allow-http: "false"
spec:
  defaultBackend:
    service:
      name: cloud-function-service
      port:
        number: 80
  tls:
  - hosts:
    - smartparent.qubitrhythm.com
    secretName: smartparent-tls
  rules:
  - host: smartparent.qubitrhythm.com
    http:
      paths:
      - path: /*
        pathType: ImplementationSpecific
        backend:
          service:
            name: cloud-function-service
            port:
              number: 80
```

## 6. Validation Workflow
```bash
# Apply configurations
kubectl apply -f k8s/cert-issuer.yaml
kubectl apply -f k8s/cloud-function-ingress.yaml

# Monitor progression
watch -n 2 "kubectl get certificaterequest,order,challenge -A"

# Verify final state
kubectl get certificate smartparent-tls -n smartparent -o wide
```

## 7. Network Requirements
```diff
+ Required Ports:
- 80/tcp : ACME HTTP-01 challenges
- 443/tcp: HTTPS traffic

+ GCP Firewall Rules:
- Allow from 0.0.0.0/0 to tag gke-cert-manager on 80/tcp
- Default GCE L7 LB rules for 443/tcp
```

## 8. Troubleshooting Guide
```bash
# Certificate Chain Inspection
kubectl get secret smartparent-tls -n smartparent -o jsonpath='{.data.tls\.crt}' | base64 -d | openssl x509 -text -noout

# ACME Challenge Debugging
kubectl describe challenge -n smartparent
kubectl logs -n cert-manager deploy/cert-manager --tail=100

# DNS Verification
dig +short smartparent.qubitrhythm.com
nslookup -type=SOA smartparent.qubitrhythm.com *******

# LB Health Checks
gcloud compute backend-services list --format="table(name,backends[].group.scope())"
```

## 9. Maintenance Procedures
```bash
# Certificate Renewal Monitoring
kubectl get certificates -w -n smartparent

# Cert-Manager Upgrade
helm upgrade cert-manager jetstack/cert-manager \
  --namespace cert-manager \
  --version v1.13.2

# DNS Record Audit
gcloud dns record-sets list --zone=smartparent-dns \
  --filter="type=NS OR type=SOA" \
  --format=json
```

## 10. Visual Workflow
```mermaid
graph TD
    A[GoDaddy NS Records] --> B[GCP Cloud DNS]
    B --> C[GCE Load Balancer]
    C --> D[K8s Ingress]
    D --> E[Cert-Manager]
    E --> F[Let's Encrypt]
    F -->|ACME Challenge| C
    C -->|Validated| E
    E -->|Issues Certificate| D
```
```bash
# Required open ports:
- 80/tcp (HTTP for ACME challenges)
- 443/tcp (HTTPS traffic)

# Firewall rules automatically managed by:
- GCE ingress controller
- Cloud Load Balancer

## 2. IAM Configuration
```bash
gcloud projects add-iam-policy-binding smartparent \
--member=serviceAccount:<EMAIL> \
--role=roles/dns.admin

gcloud projects add-iam-policy-binding smartparent \
--member=serviceAccount:<EMAIL> \
--role=roles/container.admin
```

## 3. DNS & Load Balancer Setup
```bash
# Create managed zone
gcloud dns managed-zones create smartparent-dns \
--description="Production DNS zone" \
--dns-name=smartparent.qubitrhythm.com \
--visibility=public

# Get LB IP (auto-created by ingress)
gcloud compute addresses describe smartparent-lb-ip \
--global \
--format='value(address)'

# Verify DNS propagation
dig +short smartparent.qubitrhythm.com @*******
```

## 4. Full Cert-Manager Configuration
```yaml
# k8s/cert-issuer.yaml
apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: letsencrypt-prod
spec:
  acme:
    email: <EMAIL>
    server: https://acme-v02.api.letsencrypt.org/directory
    privateKeySecretRef:
      name: letsencrypt-prod-key
    solvers:
    - http01:
        ingress:
          class: gce
          serviceType: ClusterIP
          podTemplate:
            spec:
              nodeSelector:
                cloud.google.com/gke-nodepool: default-pool
```

## 5. Complete Ingress Definition
```yaml
# k8s/cloud-function-ingress.yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: smartparent-ingress
  annotations:
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    kubernetes.io/ingress.class: "gce"
    networking.gke.io/managed-certificates: "smartparent-cert"
    kubernetes.io/ingress.allow-http: "false"
spec:
  defaultBackend:
    service:
      name: cloud-function-service
      port:
        number: 80
  tls:
  - hosts:
    - smartparent.qubitrhythm.com
    secretName: smartparent-tls
  rules:
  - host: smartparent.qubitrhythm.com
    http:
      paths:
      - path: /*
        pathType: ImplementationSpecific
        backend:
          service:
            name: cloud-function-service
            port:
              number: 80
```

## 6. Debugging Cheat Sheet
```bash
# Get challenge status
kubectl get challenges.acme.cert-manager.io -n smartparent -o wide

# Inspect certificate details
kubectl describe certificate smartparent-tls -n smartparent

# Check managed certificate state
kubectl describe managedcertificate smartparent-cert -n smartparent

# Verify DNS records
gcloud dns record-sets list --zone=smartparent-dns

# Check LB health
gcloud compute backend-services get-health $(gcloud compute backend-services list --filter="name~'k8s1'" --format="value(name)") --global

# Get ingress events
kubectl get events -n smartparent --field-selector involvedObject.kind=Ingress
```

## 7. Maintenance Procedures

### 1. DNS Configuration (GoDaddy)
```diff
- Deleted: A record pointing to GCP LB IP
+ Added: 4 NS records delegating smartparent.qubitrhythm.com subdomain to GCP Cloud DNS:
  ns-cloud-a1.googledomains.com
  ns-cloud-a2.googledomains.com
  ns-cloud-a3.googledomains.com
  ns-cloud-a4.googledomains.com
```

**Why This Works**: By delegating DNS management to GCP Cloud DNS, we:
1. Avoid manual record management
2. Enable automatic record creation by GCP
3. Ensure proper propagation between DNS systems

### 2. GCP Cloud DNS Setup
1. Created managed zone for `smartparent.qubitrhythm.com`
2. Verified domain ownership through:
   - Google Search Console
   - TXT record verification
3. Automatic A record creation by GCP Load Balancer

### 3. Cert-Manager Configuration (http01)
```yaml
# cert-issuer.yaml
apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: letsencrypt-prod
spec:
  acme:
    server: https://acme-v02.api.letsencrypt.org/directory
    email: <EMAIL>
    privateKeySecretRef:
      name: letsencrypt-prod
    solvers:
    - http01:
        ingress:
          class: gce
```

### 4. Ingress Configuration
```yaml
# cloud-function-ingress.yaml
metadata:
  annotations:
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    kubernetes.io/ingress.class: "gce"
    networking.gke.io/managed-certificates: "smartparent-cert"
spec:
  tls:
  - hosts:
    - smartparent.qubitrhythm.com
    secretName: smartparent-tls
```

### 5. Certificate Validation via HTTP01
1. Automatic HTTP challenge handling:
   - cert-manager creates temporary validation endpoints
   - GCE ingress controller routes /.well-known/acme-challenge requests
   - Self-cleaning after validation
2. Requirements:
   - Ingress controller must be publicly accessible
   - Port 80 must be open on firewall
   - No DNS records needed for validation

### 6. Validation Workflow
1. Cert-manager creates Order resource
2. Let's Encrypt issues HTTP challenge
3. GCE ingress creates temporary path:
   `/.well-known/acme-challenge/<token>`
4. Automatic response validation
5. Certificate issued and stored in Secret

### 7. Maintenance Procedures
1. Certificate renewal monitoring:
```bash
watch kubectl get certificates -n smartparent
```
2. Troubleshooting commands:
```bash
# Check challenge status
kubectl describe challenges.acme.cert-manager.io -n smartparent

# View ACME orders
kubectl get orders.acme.cert-manager.io -n smartparent

# Inspect cert-manager logs
kubectl logs -n cert-manager deploy/cert-manager
```

### 8. Architecture Diagram
```
GoDaddy NS Records -> GCP Cloud DNS Zone -> GCE Load Balancer
                     ↑                       ↑
Let's Encrypt ↔ Cert-Manager ↔ Kubernetes Ingress
```

### 9. Key Lessons Learned
1. DNS delegation eliminates manual record management
2. http01 simplifies validation for GKE ingress
