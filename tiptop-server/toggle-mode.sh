#!/bin/bash
# Script to toggle between test and production modes for TipTop

# Function to handle errors
handle_error() {
    echo "Error occurred in toggle script at line $1"
    exit 1
}

trap 'handle_error $LINENO' ERR

# Check if .env file exists, create it if not
if [ ! -f .env ]; then
    echo "Creating .env file..."
    touch .env
fi

# Function to set environment variable in .env file
set_env_var() {
    local key=$1
    local value=$2
    
    # Check if the variable already exists in the file
    if grep -q "^${key}=" .env; then
        # Replace the existing value
        sed -i "" "s|^${key}=.*|${key}=${value}|" .env
    else
        # Add the variable
        echo "${key}=${value}" >> .env
    fi
}

# Parse command line arguments
if [ "$1" == "test" ]; then
    echo "Switching to TEST mode..."
    set_env_var "TIPTOP_TEST_MODE" "true"
    MODE="test"
elif [ "$1" == "production" ]; then
    echo "Switching to PRODUCTION mode..."
    set_env_var "TIPTOP_TEST_MODE" "false"
    MODE="production"
else
    echo "Usage: $0 [test|production]"
    echo "  test       - Switch to test mode"
    echo "  production - Switch to production mode"
    exit 1
fi

# Export the environment variable for this session
export TIPTOP_TEST_MODE=$([[ "$MODE" == "test" ]] && echo "true" || echo "false")

# Update Kubernetes secrets
echo "Updating Kubernetes secrets..."
if [ "$MODE" == "test" ]; then
    echo "Using test secrets..."
    kubectl apply -f k8s-tiptop/tiptop-postgres-secrets-test.yaml -n tiptop
    kubectl apply -f k8s-tiptop/tiptop-cloud-function-secrets-test.yaml -n tiptop
    kubectl apply -f websocket-server/k8s-deploy/tiptop-websocket-secrets-test.yaml -n tiptop
else
    echo "Using production secrets..."
    kubectl apply -f k8s-tiptop/tiptop-postgres-secrets.yaml -n tiptop
    kubectl apply -f k8s-tiptop/tiptop-cloud-function-secrets.yaml -n tiptop
    kubectl apply -f websocket-server/k8s-deploy/tiptop-websocket-secrets.yaml -n tiptop
fi

# Restart deployments to pick up new configuration
echo "Restarting deployments..."
kubectl rollout restart deployment/cloud-function-tiptop-deployment -n tiptop
kubectl rollout restart deployment/tiptop-websocket-deployment -n tiptop

# Wait for deployments to be ready
echo "Waiting for deployments to be ready..."
kubectl rollout status deployment/cloud-function-tiptop-deployment -n tiptop
kubectl rollout status deployment/tiptop-websocket-deployment -n tiptop

echo "Mode switched to $MODE successfully!"
echo "Current configuration:"
echo "  - Test Mode: $([[ "$MODE" == "test" ]] && echo "ENABLED" || echo "DISABLED")"
echo "  - Cloud Function URL: $([[ "$MODE" == "test" ]] && echo "http://localhost:30080" || echo "https://tiptop.qubitrhythm.com")"
echo "  - WebSocket URL: $([[ "$MODE" == "test" ]] && echo "ws://localhost:8080" || echo "wss://ws.tiptop.qubitrhythm.com")"
