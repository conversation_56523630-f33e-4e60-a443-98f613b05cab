#!/bin/bash

# TipTop Production Configuration Verification Script
# This script verifies that all files are properly configured for GCP production deployment

echo "🔍 TipTop Production Configuration Verification"
echo "=============================================="
echo ""

# Function to check file content
check_file_content() {
    local file=$1
    local expected_content=$2
    local description=$3
    
    if [ -f "$file" ]; then
        if grep -q "$expected_content" "$file"; then
            echo "✅ $description"
            return 0
        else
            echo "❌ $description"
            echo "   Expected: $expected_content"
            echo "   File: $file"
            return 1
        fi
    else
        echo "❌ $description"
        echo "   File not found: $file"
        return 1
    fi
}

# Function to check if file exists
check_file_exists() {
    local file=$1
    local description=$2
    
    if [ -f "$file" ]; then
        echo "✅ $description"
        return 0
    else
        echo "❌ $description"
        echo "   File not found: $file"
        return 1
    fi
}

echo "📋 Checking Storage Configuration..."
echo "-----------------------------------"
issues=0

# Check PVC configuration
if ! check_file_content "k8s-tiptop/tiptop-postgres-pvc.yaml" "storageClassName: standard-rwo" "PostgreSQL PVC uses GCP storage class"; then
    ((issues++))
fi

# Check that PVC doesn't reference manual PV
if check_file_content "k8s-tiptop/tiptop-postgres-pvc.yaml" "volumeName:" "PostgreSQL PVC references manual volume (should not for GCP)"; then
    echo "❌ PostgreSQL PVC should not reference volumeName for GCP"
    ((issues++))
fi

echo ""
echo "📋 Checking Secrets Configuration..."
echo "-----------------------------------"

# Check production secrets exist
if ! check_file_exists "k8s-tiptop/tiptop-postgres-secrets.yaml" "PostgreSQL production secrets"; then
    ((issues++))
fi

if ! check_file_exists "k8s-tiptop/tiptop-cloud-function-secrets.yaml" "Cloud Function production secrets"; then
    ((issues++))
fi

if ! check_file_exists "websocket-server/k8s-deploy/tiptop-websocket-secrets.yaml" "WebSocket production secrets"; then
    ((issues++))
fi

echo ""
echo "📋 Checking Deployment Configuration..."
echo "--------------------------------------"

# Check deployment files exist
if ! check_file_exists "k8s-tiptop/tiptop-cloud-function-deployment.yaml" "Cloud Function deployment"; then
    ((issues++))
fi

if ! check_file_exists "websocket-server/k8s-deploy/tiptop-websocket-deployment.yaml" "WebSocket deployment"; then
    ((issues++))
fi

# Check ingress configuration
if ! check_file_exists "k8s-tiptop/tiptop-cloud-function-ingress.yaml" "Cloud Function ingress"; then
    ((issues++))
fi

if ! check_file_exists "websocket-server/k8s-deploy/tiptop-websocket-ingress.yaml" "WebSocket ingress"; then
    ((issues++))
fi

echo ""
echo "📋 Checking Certificate Configuration..."
echo "---------------------------------------"

if ! check_file_exists "k8s-tiptop/tiptop-cert-issuer.yaml" "Certificate issuer"; then
    ((issues++))
fi

echo ""
echo "📋 Checking Mode Configuration..."
echo "--------------------------------"

# Check mode indicator file
if check_file_exists "tiptop-mode.json" "Mode indicator file"; then
    if check_file_content "tiptop-mode.json" '"mode":"production"' "Mode set to production"; then
        echo "✅ Mode correctly set to production"
    else
        echo "❌ Mode not set to production"
        ((issues++))
    fi
else
    ((issues++))
fi

# Check extension mode file
if check_file_exists "tiptop-extension/tiptop-mode.json" "Extension mode indicator file"; then
    echo "✅ Extension mode indicator file exists"
else
    echo "⚠️  Extension mode indicator file missing (will be created by toggle script)"
fi

echo ""
echo "📋 Checking Docker Configuration..."
echo "----------------------------------"

if ! check_file_exists "cloud-function-tiptop/Dockerfile" "Cloud Function Dockerfile"; then
    ((issues++))
fi

if ! check_file_exists "websocket-server/Dockerfile" "WebSocket Dockerfile"; then
    ((issues++))
fi

echo ""
echo "📋 Summary"
echo "=========="
echo ""

if [ $issues -eq 0 ]; then
    echo "🎉 ALL CHECKS PASSED!"
    echo ""
    echo "✅ Configuration is ready for GCP production deployment"
    echo "✅ Storage configured for GCP (standard-rwo)"
    echo "✅ All required files present"
    echo "✅ Mode set to production"
    echo ""
    echo "🚀 Ready to deploy with:"
    echo "   ./deploy-to-gcp.sh YOUR_PROJECT_ID"
    echo ""
else
    echo "⚠️  $issues ISSUES FOUND"
    echo ""
    echo "❌ Configuration is NOT ready for deployment"
    echo ""
    echo "🔧 To fix issues:"
    echo "1. Run: ../tiptop-toggle-mode.sh production"
    echo "2. Run this verification script again"
    echo "3. Fix any remaining issues manually"
    echo ""
fi

echo "📚 For more information:"
echo "  - Run: ../tiptop-toggle-mode.sh production"
echo "  - Check: GCP-DEPLOYMENT-GUIDE.md"
