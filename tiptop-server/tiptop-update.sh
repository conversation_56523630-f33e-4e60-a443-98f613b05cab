#!/bin/bash
set -e

# Function to handle errors
handle_error() {
    echo "Error occurred in update script at line $1"
    exit 1
}

trap 'handle_error $LINENO' ERR

echo "Building TipTop cloud function Docker image for local development..."
docker build -t cloud-function-tiptop:local -f cloud-function-tiptop/Dockerfile cloud-function-tiptop

echo "Updating TipTop deployment..."
kubectl set image deployment/cloud-function-tiptop-deployment cloud-function-tiptop=cloud-function-tiptop:local -n tiptop

# Check rollout status
echo "Checking rollout status..."
kubectl rollout status deployment/cloud-function-tiptop-deployment -n tiptop

echo "TipTop update completed successfully!"
echo "Service should be available at http://localhost:8080"
