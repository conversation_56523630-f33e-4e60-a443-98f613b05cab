#!/bin/bash

# TipTop Migration Script: SmartParent Namespace → TipTop Namespace
# This script safely migrates TipTop from smartparent namespace to tiptop namespace

# Function to handle errors
handle_error() {
    echo "❌ Error occurred in migration script at line $1"
    exit 1
}

trap 'handle_error $LINENO' ERR

# Function to show usage
show_usage() {
    echo "Usage: $0 <GCP_PROJECT_ID> [--migrate-only|--replace-in-place]"
    echo ""
    echo "Migration strategies:"
    echo "  --migrate-only      - Create new TipTop in tiptop namespace, keep old in smartparent"
    echo "  --replace-in-place  - Replace old TipTop in smartparent namespace (RISKY)"
    echo ""
    echo "Arguments:"
    echo "  GCP_PROJECT_ID  - Your GCP project ID"
    echo ""
    echo "Examples:"
    echo "  $0 my-project-123 --migrate-only      # Safest option"
    echo "  $0 my-project-123 --replace-in-place  # Updates existing deployment"
    exit 1
}

# Parse arguments
if [ -z "$1" ]; then
    echo "❌ Error: GCP Project ID is required"
    show_usage
fi

GCP_PROJECT_ID=$1
STRATEGY=""

if [ "$2" == "--migrate-only" ]; then
    STRATEGY="migrate"
elif [ "$2" == "--replace-in-place" ]; then
    STRATEGY="replace"
else
    echo "❌ Error: Migration strategy is required"
    show_usage
fi

echo "🔄 TipTop Migration from SmartParent Namespace"
echo "=============================================="
echo "📋 Project ID: $GCP_PROJECT_ID"
echo "🎯 Strategy: $STRATEGY"
echo ""

# Check current TipTop deployment in smartparent namespace
echo "🔍 Checking current TipTop deployment in smartparent namespace..."
if ! kubectl get deployment cloud-function-tiptop-deployment -n smartparent >/dev/null 2>&1; then
    echo "❌ No TipTop deployment found in smartparent namespace!"
    echo "💡 You may be able to use the regular deployment script:"
    echo "   ./deploy-to-gcp.sh $GCP_PROJECT_ID --rolling-update"
    exit 1
fi

echo "✅ Found existing TipTop deployment in smartparent namespace"
echo ""

# Show current status
echo "📊 Current TipTop resources in smartparent namespace:"
kubectl get all -n smartparent | grep tiptop
echo ""

if [ "$STRATEGY" == "migrate" ]; then
    echo "🚀 MIGRATION STRATEGY: Create new TipTop in tiptop namespace"
    echo ""
    echo "This will:"
    echo "✅ Create new TipTop deployment in tiptop namespace"
    echo "✅ Keep old TipTop running in smartparent namespace"
    echo "✅ Allow gradual migration of traffic"
    echo "⚠️  Require manual cleanup of old deployment later"
    echo ""
    
    read -p "Continue with migration? (y/N): " continue_migration
    if [[ ! "$continue_migration" =~ ^[Yy]$ ]]; then
        echo "Migration cancelled."
        exit 0
    fi
    
    # Create tiptop namespace
    echo "📦 Creating tiptop namespace..."
    kubectl create namespace tiptop --dry-run=client -o yaml | kubectl apply -f -
    
    # Deploy new TipTop to tiptop namespace
    echo "🚀 Deploying new TipTop to tiptop namespace..."
    ./deploy-to-gcp.sh $GCP_PROJECT_ID
    
    echo ""
    echo "✅ Migration completed!"
    echo ""
    echo "📊 Current status:"
    echo "🔹 Old TipTop: Running in smartparent namespace"
    echo "🔹 New TipTop: Running in tiptop namespace"
    echo ""
    echo "🎯 Next steps:"
    echo "1. Test new TipTop deployment: https://tiptop.qubitrhythm.com"
    echo "2. Update DNS to point to new ingress (if needed)"
    echo "3. Monitor both deployments"
    echo "4. When ready, clean up old deployment:"
    echo "   kubectl delete deployment cloud-function-tiptop-deployment -n smartparent"
    echo "   kubectl delete service cloud-function-tiptop-service -n smartparent"
    echo ""
    
elif [ "$STRATEGY" == "replace" ]; then
    echo "⚠️  REPLACE-IN-PLACE STRATEGY: Update existing TipTop in smartparent namespace"
    echo ""
    echo "This will:"
    echo "✅ Update existing TipTop deployment with new code"
    echo "✅ Add WebSocket server to smartparent namespace"
    echo "⚠️  Keep TipTop in smartparent namespace (mixed with SmartParent)"
    echo "⚠️  Brief downtime during update"
    echo ""
    
    read -p "⚠️  Are you sure you want to update in-place? (y/N): " continue_replace
    if [[ ! "$continue_replace" =~ ^[Yy]$ ]]; then
        echo "Update cancelled."
        exit 0
    fi
    
    # Update deployment files to use smartparent namespace
    echo "🔧 Updating deployment files for smartparent namespace..."
    
    # Backup original files
    cp k8s-tiptop/tiptop-cloud-function-deployment.yaml k8s-tiptop/tiptop-cloud-function-deployment.yaml.bak
    cp websocket-server/k8s-deploy/tiptop-websocket-deployment.yaml websocket-server/k8s-deploy/tiptop-websocket-deployment.yaml.bak
    
    # Update namespace in deployment files
    sed -i.tmp 's/namespace: tiptop/namespace: smartparent/g' k8s-tiptop/*.yaml
    sed -i.tmp 's/namespace: tiptop/namespace: smartparent/g' websocket-server/k8s-deploy/*.yaml
    
    # Update project ID in deployment files
    sed -i.tmp "s/PROJECT_ID/$GCP_PROJECT_ID/g" k8s-tiptop/tiptop-cloud-function-deployment.yaml
    sed -i.tmp "s/PROJECT_ID/$GCP_PROJECT_ID/g" websocket-server/k8s-deploy/tiptop-websocket-deployment.yaml
    
    # Build and push images
    echo "🏗️  Building and pushing updated images..."
    
    # Build Cloud Function
    cd cloud-function-tiptop
    docker build -t gcr.io/$GCP_PROJECT_ID/tiptop-cloud-function:latest .
    docker push gcr.io/$GCP_PROJECT_ID/tiptop-cloud-function:latest
    cd ..
    
    # Build WebSocket server
    cd websocket-server
    docker build -t gcr.io/$GCP_PROJECT_ID/tiptop-websocket:latest .
    docker push gcr.io/$GCP_PROJECT_ID/tiptop-websocket:latest
    cd ..
    
    # Apply secrets to smartparent namespace
    echo "🔐 Applying secrets to smartparent namespace..."
    kubectl apply -f k8s-tiptop/tiptop-cloud-function-secrets.yaml -n smartparent
    kubectl apply -f websocket-server/k8s-deploy/tiptop-websocket-secrets.yaml -n smartparent
    
    # Update existing deployment
    echo "🔄 Updating existing TipTop deployment..."
    kubectl apply -f k8s-tiptop/tiptop-cloud-function-deployment.yaml -n smartparent
    
    # Deploy WebSocket server
    echo "🔌 Deploying WebSocket server..."
    kubectl apply -f websocket-server/k8s-deploy/tiptop-websocket-deployment.yaml -n smartparent
    kubectl apply -f websocket-server/k8s-deploy/tiptop-websocket-service.yaml -n smartparent
    
    # Wait for rollout
    echo "⏳ Waiting for deployment to complete..."
    kubectl rollout status deployment/cloud-function-tiptop-deployment -n smartparent
    kubectl rollout status deployment/tiptop-websocket -n smartparent
    
    # Restore original files
    echo "🔄 Restoring original deployment files..."
    mv k8s-tiptop/tiptop-cloud-function-deployment.yaml.bak k8s-tiptop/tiptop-cloud-function-deployment.yaml
    mv websocket-server/k8s-deploy/tiptop-websocket-deployment.yaml.bak websocket-server/k8s-deploy/tiptop-websocket-deployment.yaml
    
    # Clean up temp files
    rm -f k8s-tiptop/*.tmp websocket-server/k8s-deploy/*.tmp
    
    echo ""
    echo "✅ In-place update completed!"
    echo ""
    echo "📊 Updated TipTop resources in smartparent namespace:"
    kubectl get all -n smartparent | grep tiptop
    echo ""
    echo "⚠️  Note: TipTop is now running in smartparent namespace alongside SmartParent"
    echo "💡 Consider migrating to separate tiptop namespace in the future for better isolation"
fi

echo ""
echo "🎯 Deployment completed successfully!"
