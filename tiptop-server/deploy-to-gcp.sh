#!/bin/bash

# TipTop GCP Deployment Script
# This script deploys the complete TipTop server infrastructure to GCP

# Function to handle errors
handle_error() {
    echo "❌ Error occurred in deployment script at line $1"
    exit 1
}

trap 'handle_error $LINENO' ERR

# Function to show usage
show_usage() {
    echo "Usage: $0 <GCP_PROJECT_ID> [--update-only|--rolling-update]"
    echo ""
    echo "Arguments:"
    echo "  GCP_PROJECT_ID  - Your GCP project ID"
    echo ""
    echo "Options:"
    echo "  --update-only     - Only update existing deployments (skip initial setup)"
    echo "  --rolling-update  - Zero-downtime rolling update (recommended for production)"
    echo ""
    echo "Examples:"
    echo "  $0 my-gcp-project-123                    # Full deployment (new setup)"
    echo "  $0 my-gcp-project-123 --update-only     # Update with brief downtime"
    echo "  $0 my-gcp-project-123 --rolling-update  # Zero-downtime update"
    exit 1
}

# Parse command line arguments
if [ -z "$1" ]; then
    echo "❌ Error: GCP Project ID is required"
    show_usage
fi

GCP_PROJECT_ID=$1
UPDATE_ONLY=false
ROLLING_UPDATE=false

if [ "$2" == "--update-only" ]; then
    UPDATE_ONLY=true
elif [ "$2" == "--rolling-update" ]; then
    ROLLING_UPDATE=true
    UPDATE_ONLY=true  # Rolling update implies update-only
fi

echo "🚀 Starting TipTop deployment to GCP..."
echo "📋 Project ID: $GCP_PROJECT_ID"
echo "🔄 Update only: $UPDATE_ONLY"
echo "🔄 Rolling update: $ROLLING_UPDATE"
echo ""

# Check if required tools are installed
echo "🔍 Checking required tools..."
command -v gcloud >/dev/null 2>&1 || { echo "❌ gcloud CLI is required but not installed."; exit 1; }
command -v kubectl >/dev/null 2>&1 || { echo "❌ kubectl is required but not installed."; exit 1; }
command -v docker >/dev/null 2>&1 || { echo "❌ docker is required but not installed."; exit 1; }

# Set GCP project
echo "🔧 Setting GCP project..."
gcloud config set project $GCP_PROJECT_ID

# Configure Docker for GCR
echo "🐳 Configuring Docker for Google Container Registry..."
gcloud auth configure-docker

# Get GKE credentials (assuming cluster name is 'tiptop-cluster')
echo "☸️  Getting GKE credentials..."
gcloud container clusters get-credentials tiptop-cluster --region=us-central1 || {
    echo "⚠️  Cluster 'tiptop-cluster' not found. Please update the cluster name and region."
    echo "Available clusters:"
    gcloud container clusters list
    exit 1
}

if [ "$UPDATE_ONLY" = false ]; then
    echo ""
    echo "🏗️  FULL DEPLOYMENT MODE"
    echo "This will deploy all components from scratch..."
    echo ""
    
    # Create namespace if it doesn't exist
    echo "📦 Creating tiptop namespace..."
    kubectl create namespace tiptop --dry-run=client -o yaml | kubectl apply -f -
    
    # Apply secrets
    echo "🔐 Applying production secrets..."
    kubectl apply -f k8s-tiptop/tiptop-postgres-secrets.yaml -n tiptop
    kubectl apply -f k8s-tiptop/tiptop-cloud-function-secrets.yaml -n tiptop
    kubectl apply -f websocket-server/k8s-deploy/tiptop-websocket-secrets.yaml -n tiptop
    
    # Deploy PostgreSQL if not exists
    echo "🗄️  Deploying PostgreSQL..."
    kubectl apply -f k8s-tiptop/tiptop-postgres-deployment.yaml -n tiptop
    kubectl apply -f k8s-tiptop/tiptop-postgres-service.yaml -n tiptop
    
    # Wait for PostgreSQL to be ready
    echo "⏳ Waiting for PostgreSQL to be ready..."
    kubectl wait --for=condition=available --timeout=300s deployment/tiptop-postgres -n tiptop
else
    echo ""
    echo "🔄 UPDATE MODE"
    echo "This will update existing deployments..."
    echo ""
fi

# Build and push Cloud Function image
echo "🏗️  Building Cloud Function image..."
cd cloud-function-tiptop
docker build -t gcr.io/$GCP_PROJECT_ID/tiptop-cloud-function:latest .
echo "📤 Pushing Cloud Function image to GCR..."
docker push gcr.io/$GCP_PROJECT_ID/tiptop-cloud-function:latest
cd ..

# Build and push WebSocket server image
echo "🏗️  Building WebSocket server image..."
cd websocket-server
docker build -t gcr.io/$GCP_PROJECT_ID/tiptop-websocket:latest .
echo "📤 Pushing WebSocket server image to GCR..."
docker push gcr.io/$GCP_PROJECT_ID/tiptop-websocket:latest
cd ..

# Update deployment files with correct project ID
echo "🔧 Updating deployment files with project ID..."
sed -i.bak "s/PROJECT_ID/$GCP_PROJECT_ID/g" k8s-tiptop/tiptop-cloud-function-deployment.yaml
sed -i.bak "s/PROJECT_ID/$GCP_PROJECT_ID/g" websocket-server/k8s-deploy/tiptop-websocket-deployment.yaml

# Deploy Cloud Function
echo "☁️  Deploying Cloud Function..."
if [ "$ROLLING_UPDATE" = true ]; then
    echo "🔄 Performing rolling update for Cloud Function..."
    # Force rolling update by updating image tag with timestamp
    kubectl patch deployment cloud-function-tiptop-deployment -n tiptop -p \
        "{\"spec\":{\"template\":{\"metadata\":{\"annotations\":{\"deployment.kubernetes.io/revision\":\"$(date +%s)\"}}}}}"
    kubectl set image deployment/cloud-function-tiptop-deployment \
        cloud-function-tiptop=gcr.io/$GCP_PROJECT_ID/tiptop-cloud-function:latest -n tiptop
else
    kubectl apply -f k8s-tiptop/tiptop-cloud-function-deployment.yaml -n tiptop
fi
kubectl apply -f k8s-tiptop/tiptop-cloud-function-service.yaml -n tiptop

# Deploy WebSocket server
echo "🔌 Deploying WebSocket server..."
if [ "$ROLLING_UPDATE" = true ]; then
    echo "🔄 Performing rolling update for WebSocket server..."
    # Check if WebSocket deployment exists
    if kubectl get deployment tiptop-websocket -n tiptop >/dev/null 2>&1; then
        # Force rolling update by updating image tag with timestamp
        kubectl patch deployment tiptop-websocket -n tiptop -p \
            "{\"spec\":{\"template\":{\"metadata\":{\"annotations\":{\"deployment.kubernetes.io/revision\":\"$(date +%s)\"}}}}}"
        kubectl set image deployment/tiptop-websocket \
            tiptop-websocket=gcr.io/$GCP_PROJECT_ID/tiptop-websocket:latest -n tiptop
    else
        echo "⚠️  WebSocket deployment doesn't exist, creating new deployment..."
        kubectl apply -f websocket-server/k8s-deploy/tiptop-websocket-deployment.yaml -n tiptop
    fi
else
    kubectl apply -f websocket-server/k8s-deploy/tiptop-websocket-deployment.yaml -n tiptop
fi
kubectl apply -f websocket-server/k8s-deploy/tiptop-websocket-service.yaml -n tiptop

if [ "$UPDATE_ONLY" = false ]; then
    # Deploy ingress (only for full deployment)
    echo "🌐 Deploying ingress..."
    kubectl apply -f k8s-tiptop/tiptop-cloud-function-ingress.yaml -n tiptop
    kubectl apply -f websocket-server/k8s-deploy/tiptop-websocket-ingress.yaml -n tiptop
fi

# Wait for deployments to be ready
echo "⏳ Waiting for deployments to be ready..."
if [ "$ROLLING_UPDATE" = true ]; then
    echo "🔄 Monitoring rolling update progress..."
    echo "📊 Cloud Function rollout status:"
    kubectl rollout status deployment/cloud-function-tiptop-deployment -n tiptop --timeout=600s

    if kubectl get deployment tiptop-websocket -n tiptop >/dev/null 2>&1; then
        echo "📊 WebSocket server rollout status:"
        kubectl rollout status deployment/tiptop-websocket -n tiptop --timeout=600s
    fi
else
    kubectl wait --for=condition=available --timeout=600s deployment/cloud-function-tiptop-deployment -n tiptop
    if kubectl get deployment tiptop-websocket -n tiptop >/dev/null 2>&1; then
        kubectl wait --for=condition=available --timeout=600s deployment/tiptop-websocket -n tiptop
    fi
fi

# Restore deployment files
echo "🔄 Restoring deployment files..."
mv k8s-tiptop/tiptop-cloud-function-deployment.yaml.bak k8s-tiptop/tiptop-cloud-function-deployment.yaml
mv websocket-server/k8s-deploy/tiptop-websocket-deployment.yaml.bak websocket-server/k8s-deploy/tiptop-websocket-deployment.yaml

echo ""
echo "✅ Deployment completed successfully!"
echo ""
echo "📊 Deployment Status:"
kubectl get pods -n tiptop
echo ""
echo "🌐 Services:"
kubectl get services -n tiptop
echo ""
echo "🔗 Ingress:"
kubectl get ingress -n tiptop
echo ""
echo "🎯 Next Steps:"
echo "1. Update DNS records to point to the ingress IP addresses"
echo "2. Wait for SSL certificates to be issued (may take a few minutes)"
echo "3. Test the endpoints:"
echo "   - API: https://tiptop.qubitrhythm.com/tiptop"
echo "   - WebSocket: wss://ws.tiptop.qubitrhythm.com"
echo ""
echo "🔍 Monitoring:"
echo "  - API logs: kubectl logs deployment/cloud-function-tiptop-deployment -n tiptop -f"
echo "  - WebSocket logs: kubectl logs deployment/tiptop-websocket -n tiptop -f"
echo "  - All pods: kubectl get pods -n tiptop -w"
