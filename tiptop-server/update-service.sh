#!/bin/bash
set -e

# Function to handle errors
handle_error() {
    echo "Error occurred in update service script at line $1"
    exit 1
}

trap 'handle_error $LINENO' ERR

echo "Updating cloud function TipTop service to use NodePort..."
kubectl apply -f k8s-tiptop/tiptop-cloud-function-service.yaml -n tiptop

echo "Service updated successfully!"
echo "Cloud function TipTop is now available at:"
echo "  - HTTP: http://localhost:30080"
echo "  - HTTPS: https://localhost:30443"

# Check if the service is properly configured
echo "Verifying service configuration..."
kubectl get service cloud-function-tiptop-service -n tiptop

echo "You can now access the service directly without port forwarding."
echo "Make sure to update your extension to use the new URLs."
