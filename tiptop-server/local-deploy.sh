#!/bin/bash
set -e

# Function to handle errors
handle_error() {
    echo "Error occurred in deploy script at line $1"
    exit 1
}

trap 'handle_error $LINENO' ERR

# Create namespace if it doesn't exist
echo "Creating namespace..."
kubectl create namespace tiptop --dry-run=client -o yaml | kubectl apply -f -

# Apply configurations with proper order and verification
echo "Applying Kubernetes configurations..."

echo "Creating secrets..."
kubectl apply -f k8s/postgres-secrets.yaml -n tiptop
kubectl apply -f k8s/cloud-function-secrets.yaml -n tiptop

echo "Creating storage..."
kubectl apply -f k8s/postgres-pvc.yaml -n tiptop
# Wait for PVC to be bound
echo "Waiting for PVC to be bound..."
kubectl wait --for=condition=bound pvc/postgres-pvc -n tiptop --timeout=60s

echo "Deploying PostgreSQL..."
kubectl apply -f k8s/postgres-deployment.yaml -n tiptop
kubectl apply -f k8s/postgres-service.yaml -n tiptop

# Wait for PostgreSQL pod to be ready
echo "Waiting for PostgreSQL to be ready..."
kubectl wait --for=condition=ready pod -l app=postgres -n tiptop --timeout=180s

echo "Initializing database..."
kubectl apply -f k8s/db-init-configmap.yaml -n tiptop
kubectl apply -f k8s/db-init-job.yaml -n tiptop

# Wait for database initialization to complete
echo "Waiting for database initialization..."
kubectl wait --for=condition=complete job/db-init -n tiptop --timeout=300s

echo "PostgreSQL deployment completed successfully!"

# Build and push cloud function image
echo "Building cloud function TipTop Docker image..."
docker build -t cloud-function-tiptop:local -f cloud-function-tiptop/Dockerfile cloud-function-tiptop

echo "Deploying cloud function TipTop..."
kubectl apply -f k8s-tiptop/cloud-function-tiptop-deployment.yaml -n tiptop
kubectl apply -f k8s-tiptop/cloud-function-tiptop-service.yaml -n tiptop

# Wait for cloud function deployment to be ready
echo "Waiting for cloud function TipTop to be ready..."
kubectl wait --for=condition=available deployment/cloud-function-tiptop-deployment -n tiptop --timeout=180s

# Verify all deployments
echo "Verifying deployments..."
kubectl get pods -n tiptop
kubectl get services -n tiptop
kubectl get deployments -n tiptop

echo "All deployments completed successfully!"

# Get the NodePort assigned to the service
NODE_PORT=$(kubectl get service cloud-function-tiptop-service -n tiptop -o jsonpath='{.spec.ports[0].nodePort}')
if [ -n "$NODE_PORT" ]; then
    echo "Cloud function TipTop is available at http://localhost:$NODE_PORT"
    echo "You can also access it via port forwarding at http://localhost:8080"
else
    echo "Cloud function TipTop will be available at http://localhost:8080 via port forwarding"
fi

# Port forward to make the service accessible locally on a consistent port
echo "Setting up port forwarding for cloud-function-tiptop-service..."
kubectl port-forward service/cloud-function-tiptop-service 8080:80 -n tiptop &
echo "Port forwarding set up. Press Ctrl+C to stop."
