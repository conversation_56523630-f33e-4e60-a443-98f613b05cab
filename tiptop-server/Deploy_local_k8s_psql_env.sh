#!/bin/bash
set -e

# Function to handle errors
handle_error() {
    echo "Error occurred in deploy script at line $1"
    exit 1
}

trap 'handle_error $LINENO' ERR

# Ensure we're using the local Docker Desktop Kubernetes context
echo "Switching to local Docker Desktop Kubernetes context..."
kubectl config use-context docker-desktop

# Verify we're connected to the local cluster
echo "Verifying connection to local cluster..."
if ! kubectl get nodes | grep -q "docker-desktop"; then
    echo "Error: Not connected to Docker Desktop Kubernetes cluster"
    echo "Current context: $(kubectl config current-context)"
    echo "Available contexts:"
    kubectl config get-contexts
    exit 1
fi

echo "Successfully connected to Docker Desktop Kubernetes cluster"

# Create namespace if it doesn't exist
echo "Creating namespace..."
kubectl create namespace tiptop --dry-run=client -o yaml | kubectl apply -f -

# Apply configurations with proper order and verification
echo "Applying Kubernetes configurations..."

echo "Creating secrets..."
kubectl apply -f k8s-tiptop/tiptop-postgres-secrets.yaml -n tiptop
kubectl apply -f k8s-tiptop/tiptop-cloud-function-secrets.yaml -n tiptop

echo "Creating storage..."
# Create directory for PV data
mkdir -p /Users/<USER>/AI/tiptop/tiptop-data
chmod 777 /Users/<USER>/AI/tiptop/tiptop-data
# First create the PV
kubectl apply -f k8s-tiptop/tiptop-postgres-pv.yaml
echo "Creating PVC..."
kubectl apply -f k8s-tiptop/tiptop-postgres-pvc.yaml -n tiptop
# Check if PVC is bound
echo "Checking if PVC is bound..."
PVC_STATUS=""
MAX_RETRIES=10
RETRY_COUNT=0

while [ "$PVC_STATUS" != "Bound" ] && [ $RETRY_COUNT -lt $MAX_RETRIES ]; do
  PVC_STATUS=$(kubectl get pvc tiptop-postgres-pvc -n tiptop -o jsonpath='{.status.phase}' 2>/dev/null || echo "NotFound")

  if [ "$PVC_STATUS" == "Bound" ]; then
    echo "PVC is bound successfully!"
    break
  fi

  echo "PVC status: $PVC_STATUS. Waiting for PVC to be bound... (Attempt $((RETRY_COUNT+1))/$MAX_RETRIES)"
  RETRY_COUNT=$((RETRY_COUNT+1))
  sleep 5
done

if [ "$PVC_STATUS" != "Bound" ]; then
  echo "Error: PVC failed to bind after $MAX_RETRIES attempts."
  echo "Current PVC status:"
  kubectl get pvc -n tiptop
  echo "Current PV status:"
  kubectl get pv
  exit 1
fi

echo "Deploying PostgreSQL..."
kubectl apply -f k8s-tiptop/tiptop-postgres-deployment.yaml -n tiptop
kubectl apply -f k8s-tiptop/tiptop-postgres-service.yaml -n tiptop

# Wait for PostgreSQL pod to be ready
echo "Waiting for PostgreSQL to be ready..."
PG_READY=false
MAX_PG_RETRIES=12
PG_RETRY_COUNT=0

while [ "$PG_READY" != "true" ] && [ $PG_RETRY_COUNT -lt $MAX_PG_RETRIES ]; do
  # Check if any pods with the app=tiptop-postgres label exist and are ready
  READY_PODS=$(kubectl get pods -l app=tiptop-postgres -n tiptop -o jsonpath='{.items[*].status.containerStatuses[0].ready}' 2>/dev/null || echo "")

  if [ "$READY_PODS" == "true" ]; then
    PG_READY=true
    echo "PostgreSQL pod is ready!"
    break
  fi

  echo "PostgreSQL pod not ready yet. Waiting... (Attempt $((PG_RETRY_COUNT+1))/$MAX_PG_RETRIES)"
  kubectl get pods -l app=tiptop-postgres -n tiptop
  PG_RETRY_COUNT=$((PG_RETRY_COUNT+1))
  sleep 10
done

if [ "$PG_READY" != "true" ]; then
  echo "Error: PostgreSQL pod failed to become ready after $MAX_PG_RETRIES attempts."
  echo "Current pod status:"
  kubectl get pods -l app=tiptop-postgres -n tiptop
  echo "Pod details:"
  kubectl describe pods -l app=tiptop-postgres -n tiptop
  exit 1
fi

echo "Initializing database..."
kubectl apply -f k8s-tiptop/tiptop-db-init-configmap.yaml -n tiptop
kubectl apply -f k8s-tiptop/tiptop-db-init-job.yaml -n tiptop

# Wait for database initialization to complete
echo "Waiting for database initialization..."
DB_INIT_COMPLETE=false
MAX_DB_INIT_RETRIES=15
DB_INIT_RETRY_COUNT=0

while [ "$DB_INIT_COMPLETE" != "true" ] && [ $DB_INIT_RETRY_COUNT -lt $MAX_DB_INIT_RETRIES ]; do
  # Check if the job is complete
  JOB_STATUS=$(kubectl get job tiptop-db-init -n tiptop -o jsonpath='{.status.conditions[?(@.type=="Complete")].status}' 2>/dev/null || echo "")

  if [ "$JOB_STATUS" == "True" ]; then
    DB_INIT_COMPLETE=true
    echo "Database initialization completed successfully!"
    break
  fi

  # Check if the job failed
  JOB_FAILED=$(kubectl get job tiptop-db-init -n tiptop -o jsonpath='{.status.conditions[?(@.type=="Failed")].status}' 2>/dev/null || echo "")
  if [ "$JOB_FAILED" == "True" ]; then
    echo "Error: Database initialization job failed."
    kubectl get job tiptop-db-init -n tiptop
    kubectl logs job/tiptop-db-init -n tiptop
    exit 1
  fi

  echo "Database initialization not complete yet. Waiting... (Attempt $((DB_INIT_RETRY_COUNT+1))/$MAX_DB_INIT_RETRIES)"
  kubectl get job tiptop-db-init -n tiptop
  DB_INIT_RETRY_COUNT=$((DB_INIT_RETRY_COUNT+1))
  sleep 10
done

if [ "$DB_INIT_COMPLETE" != "true" ]; then
  echo "Error: Database initialization job did not complete after $MAX_DB_INIT_RETRIES attempts."
  echo "Current job status:"
  kubectl get job tiptop-db-init -n tiptop
  echo "Job details:"
  kubectl describe job tiptop-db-init -n tiptop
  echo "Job logs:"
  kubectl logs job/tiptop-db-init -n tiptop
  exit 1
fi

echo "PostgreSQL deployment completed successfully!"

# Build and push cloud function image
echo "Building cloud function TipTop Docker image..."
docker build -t cloud-function-tiptop:local -f cloud-function-tiptop/Dockerfile cloud-function-tiptop

echo "Deploying cloud function TipTop..."
kubectl apply -f k8s-tiptop/tiptop-cloud-function-deployment.yaml -n tiptop
kubectl apply -f k8s-tiptop/tiptop-cloud-function-service.yaml -n tiptop

# Wait for cloud function deployment to be ready
echo "Waiting for cloud function TipTop to be ready..."
CF_READY=false
MAX_CF_RETRIES=18
CF_RETRY_COUNT=0

while [ "$CF_READY" != "true" ] && [ $CF_RETRY_COUNT -lt $MAX_CF_RETRIES ]; do
  # Check if the deployment is available
  DEPLOYMENT_STATUS=$(kubectl get deployment cloud-function-tiptop-deployment -n tiptop -o jsonpath='{.status.conditions[?(@.type=="Available")].status}' 2>/dev/null || echo "")

  if [ "$DEPLOYMENT_STATUS" == "True" ]; then
    CF_READY=true
    echo "Cloud function deployment is ready!"
    break
  fi

  echo "Cloud function deployment not ready yet. Waiting... (Attempt $((CF_RETRY_COUNT+1))/$MAX_CF_RETRIES)"
  kubectl get deployment cloud-function-tiptop-deployment -n tiptop
  kubectl get pods -l app=cloud-function-tiptop -n tiptop
  CF_RETRY_COUNT=$((CF_RETRY_COUNT+1))
  sleep 10
done

if [ "$CF_READY" != "true" ]; then
  echo "Error: Cloud function deployment failed to become ready after $MAX_CF_RETRIES attempts."
  echo "Current deployment status:"
  kubectl get deployment cloud-function-tiptop-deployment -n tiptop
  echo "Pod status:"
  kubectl get pods -l app=cloud-function-tiptop -n tiptop
  echo "Pod details:"
  kubectl describe pods -l app=cloud-function-tiptop -n tiptop
  exit 1
fi

# Verify all deployments
echo "Verifying deployments..."
kubectl get pods -n tiptop
kubectl get services -n tiptop
kubectl get deployments -n tiptop

echo "All deployments completed successfully!"

# Display service access information
echo "Cloud function TipTop is now available at:"
echo "  - HTTP: http://localhost:30080"
echo "  - HTTPS: https://localhost:30443"
echo ""
echo "You can use these URLs in your browser or update the extension to use them."
echo "The service will remain accessible as long as the Kubernetes cluster is running."
